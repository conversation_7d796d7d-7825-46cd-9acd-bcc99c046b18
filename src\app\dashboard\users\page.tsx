'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { supabase, createUser } from '@/lib/supabase';
import { User, Camp, Company, UserRole } from '@/types/database';

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [camps, setCamps] = useState<Camp[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    full_name: '',
    phone: '',
    role: 'operator' as UserRole,
    camp_id: '',
    company_id: ''
  });

  useEffect(() => {
    loadUsers();
    loadCamps();
    loadCompanies();
  }, []);

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          camp:camps(id, name),
          company:companies(id, name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCamps = async () => {
    try {
      const { data, error } = await supabase
        .from('camps')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setCamps(data || []);
    } catch (error) {
      console.error('Error loading camps:', error);
    }
  };

  const loadCompanies = async () => {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('name');

      if (error) throw error;
      setCompanies(data || []);
      if (data && data.length > 0 && !formData.company_id) {
        setFormData(prev => ({ ...prev, company_id: data[0].id }));
      }
    } catch (error) {
      console.error('Error loading companies:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      if (!formData.email || !formData.password || !formData.full_name || !formData.role) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      setIsCreating(true);

      await createUser({
        email: formData.email,
        password: formData.password,
        full_name: formData.full_name,
        phone: formData.phone || undefined,
        role: formData.role,
        camp_id: formData.camp_id || undefined,
        company_id: formData.company_id || undefined
      });

      setShowAddModal(false);
      setFormData({
        email: '',
        password: '',
        full_name: '',
        phone: '',
        role: 'operator',
        camp_id: '',
        company_id: companies[0]?.id || ''
      });
      loadUsers();
      alert('تم إنشاء المستخدم بنجاح');
    } catch (error: any) {
      console.error('Error creating user:', error);
      alert(`فشل في إنشاء المستخدم: ${error.message}`);
    } finally {
      setIsCreating(false);
    }
  };

  const handleToggleActive = async (user: User) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !user.is_active })
        .eq('id', user.id);

      if (error) throw error;
      loadUsers();
    } catch (error) {
      console.error('Error updating user status:', error);
      alert('فشل في تحديث حالة المستخدم');
    }
  };

  const getRoleDisplayName = (role: UserRole) => {
    const roleNames = {
      admin: 'مدير النظام',
      camp_manager: 'مدير كمب',
      accountant: 'محاسب',
      operator: 'عامل'
    };
    return roleNames[role] || role;
  };

  const resetForm = () => {
    setFormData({
      email: '',
      password: '',
      full_name: '',
      phone: '',
      role: 'operator',
      camp_id: '',
      company_id: companies[0]?.id || ''
    });
    setShowAddModal(false);
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المستخدمين</h1>
            <p className="mt-1 text-sm text-gray-600">
              إضافة وإدارة مستخدمي النظام
            </p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary"
          >
            إضافة مستخدم جديد
          </button>
        </div>

        {/* Users Table */}
        <div className="card">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <div className="table-container">
              <table className="table">
                <thead>
                  <tr>
                    <th>الاسم</th>
                    <th>البريد الإلكتروني</th>
                    <th>الهاتف</th>
                    <th>الدور</th>
                    <th>الكمب</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td className="font-medium">{user.full_name}</td>
                      <td>{user.email}</td>
                      <td>{user.phone || '-'}</td>
                      <td>
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {getRoleDisplayName(user.role)}
                        </span>
                      </td>
                      <td>{user.camp?.name || '-'}</td>
                      <td>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.is_active
                            ? 'bg-success-100 text-success-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {user.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td>
                        <div className="flex space-x-2 space-x-reverse">
                          <button
                            onClick={() => handleToggleActive(user)}
                            className={`text-sm px-2 py-1 rounded ${
                              user.is_active
                                ? 'text-red-600 hover:text-red-900'
                                : 'text-green-600 hover:text-green-900'
                            }`}
                            title={user.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                          >
                            {user.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {users.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">لا يوجد مستخدمين</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add User Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">إضافة مستخدم جديد</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="input-field"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    كلمة المرور *
                  </label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className="input-field"
                    placeholder="كلمة مرور قوية"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الاسم الكامل *
                  </label>
                  <input
                    type="text"
                    value={formData.full_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                    className="input-field"
                    placeholder="الاسم الكامل"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="input-field"
                    placeholder="07xxxxxxxxx"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الدور *
                  </label>
                  <select
                    value={formData.role}
                    onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as UserRole }))}
                    className="input-field"
                  >
                    <option value="operator">عامل</option>
                    <option value="accountant">محاسب</option>
                    <option value="camp_manager">مدير كمب</option>
                    <option value="admin">مدير النظام</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الكمب
                  </label>
                  <select
                    value={formData.camp_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, camp_id: e.target.value }))}
                    className="input-field"
                  >
                    <option value="">بدون كمب محدد</option>
                    {camps.map((camp) => (
                      <option key={camp.id} value={camp.id}>
                        {camp.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3 space-x-reverse mt-6">
                <button
                  onClick={resetForm}
                  className="btn-secondary"
                  disabled={isCreating}
                >
                  إلغاء
                </button>
                <button
                  onClick={handleSubmit}
                  className="btn-primary"
                  disabled={isCreating}
                >
                  {isCreating ? 'جاري الإنشاء...' : 'إنشاء المستخدم'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}
