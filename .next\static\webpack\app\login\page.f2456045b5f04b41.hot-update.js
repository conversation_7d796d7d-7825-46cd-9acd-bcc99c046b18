"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkUserPermissions: function() { return /* binding */ checkUserPermissions; },\n/* harmony export */   createUser: function() { return /* binding */ createUser; },\n/* harmony export */   getCampsByCompany: function() { return /* binding */ getCampsByCompany; },\n/* harmony export */   getCompanies: function() { return /* binding */ getCompanies; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getUserProfile: function() { return /* binding */ getUserProfile; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://iueprblbgbqahhzwzmzp.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml1ZXByYmxiZ2JxYWhoend6bXpwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQzNTUsImV4cCI6MjA2NTIzMDM1NX0.579OWXE1sGDmqbzsE8GXe4vv2QR1YGa2jh4m0EVYv5E\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// دالة للحصول على المستخدم الحالي\nconst getCurrentUser = async ()=>{\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) {\n        console.error(\"Error getting current user:\", error);\n        return null;\n    }\n    return user;\n};\n// دالة للتحقق من صلاحيات المستخدم\nconst checkUserPermissions = async (requiredRole)=>{\n    const user = await getCurrentUser();\n    if (!user) return false;\n    const { data: userProfile, error } = await supabase.from(\"users\").select(\"role, camp_id, company_id\").eq(\"id\", user.id).single();\n    if (error || !userProfile) return false;\n    // منطق التحقق من الصلاحيات\n    switch(requiredRole){\n        case \"admin\":\n            return userProfile.role === \"admin\";\n        case \"camp_manager\":\n            return [\n                \"admin\",\n                \"camp_manager\"\n            ].includes(userProfile.role);\n        case \"accountant\":\n            return [\n                \"admin\",\n                \"camp_manager\",\n                \"accountant\"\n            ].includes(userProfile.role);\n        case \"operator\":\n            return [\n                \"admin\",\n                \"camp_manager\",\n                \"accountant\",\n                \"operator\"\n            ].includes(userProfile.role);\n        default:\n            return false;\n    }\n};\n// دالة للحصول على معلومات المستخدم الكاملة\nconst getUserProfile = async ()=>{\n    const user = await getCurrentUser();\n    if (!user) return null;\n    const { data: userProfile, error } = await supabase.from(\"users\").select(\"\\n      *,\\n      camp:camps(*),\\n      company:companies(*)\\n    \").eq(\"id\", user.id).single();\n    if (error) {\n        console.error(\"Error getting user profile:\", error);\n        return null;\n    }\n    return userProfile;\n};\n// دالة لتسجيل الدخول\nconst signIn = async (email, password)=>{\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n// دالة لتسجيل الخروج\nconst signOut = async ()=>{\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        throw new Error(error.message);\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    // إنشاء المستخدم في نظام المصادقة\n    const { data: authData, error: authError } = await supabase.auth.signUp({\n        email: userData.email,\n        password: userData.password\n    });\n    if (authError) {\n        throw new Error(authError.message);\n    }\n    if (!authData.user) {\n        throw new Error(\"فشل في إنشاء المستخدم\");\n    }\n    // إضافة بيانات المستخدم إلى جدول المستخدمين\n    const { data: userProfile, error: profileError } = await supabase.from(\"users\").insert({\n        id: authData.user.id,\n        email: userData.email,\n        full_name: userData.full_name,\n        phone: userData.phone,\n        role: userData.role,\n        camp_id: userData.camp_id,\n        company_id: userData.company_id\n    }).select().single();\n    if (profileError) {\n        throw new Error(profileError.message);\n    }\n    return {\n        authData,\n        userProfile\n    };\n};\n// دالة للحصول على الكمبات حسب الشركة\nconst getCampsByCompany = async (companyId)=>{\n    const { data, error } = await supabase.from(\"camps\").select(\"*\").eq(\"company_id\", companyId).eq(\"is_active\", true).order(\"name\");\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n// دالة للحصول على جميع الشركات\nconst getCompanies = async ()=>{\n    const { data, error } = await supabase.from(\"companies\").select(\"*\").order(\"name\");\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFxRDtBQUdyRCxNQUFNQyxjQUFjQywwQ0FBb0M7QUFDeEQsTUFBTUcsa0JBQWtCSCxrTkFBeUM7QUFFMUQsTUFBTUssV0FBV1AsbUVBQVlBLENBQVdDLGFBQWFJLGlCQUFpQjtBQUU3RSxrQ0FBa0M7QUFDM0IsTUFBTUcsaUJBQWlCO0lBQzVCLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosU0FBU0ssSUFBSSxDQUFDQyxPQUFPO0lBQzdELElBQUlGLE9BQU87UUFDVEcsUUFBUUgsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBTztJQUNUO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0FBRUYsa0NBQWtDO0FBQzNCLE1BQU1LLHVCQUF1QixPQUFPQztJQUN6QyxNQUFNTixPQUFPLE1BQU1GO0lBQ25CLElBQUksQ0FBQ0UsTUFBTSxPQUFPO0lBRWxCLE1BQU0sRUFBRUQsTUFBTVEsV0FBVyxFQUFFTixLQUFLLEVBQUUsR0FBRyxNQUFNSixTQUN4Q1csSUFBSSxDQUFDLFNBQ0xDLE1BQU0sQ0FBQyw2QkFDUEMsRUFBRSxDQUFDLE1BQU1WLEtBQUtXLEVBQUUsRUFDaEJDLE1BQU07SUFFVCxJQUFJWCxTQUFTLENBQUNNLGFBQWEsT0FBTztJQUVsQywyQkFBMkI7SUFDM0IsT0FBUUQ7UUFDTixLQUFLO1lBQ0gsT0FBT0MsWUFBWU0sSUFBSSxLQUFLO1FBQzlCLEtBQUs7WUFDSCxPQUFPO2dCQUFDO2dCQUFTO2FBQWUsQ0FBQ0MsUUFBUSxDQUFDUCxZQUFZTSxJQUFJO1FBQzVELEtBQUs7WUFDSCxPQUFPO2dCQUFDO2dCQUFTO2dCQUFnQjthQUFhLENBQUNDLFFBQVEsQ0FBQ1AsWUFBWU0sSUFBSTtRQUMxRSxLQUFLO1lBQ0gsT0FBTztnQkFBQztnQkFBUztnQkFBZ0I7Z0JBQWM7YUFBVyxDQUFDQyxRQUFRLENBQUNQLFlBQVlNLElBQUk7UUFDdEY7WUFDRSxPQUFPO0lBQ1g7QUFDRixFQUFFO0FBRUYsMkNBQTJDO0FBQ3BDLE1BQU1FLGlCQUFpQjtJQUM1QixNQUFNZixPQUFPLE1BQU1GO0lBQ25CLElBQUksQ0FBQ0UsTUFBTSxPQUFPO0lBRWxCLE1BQU0sRUFBRUQsTUFBTVEsV0FBVyxFQUFFTixLQUFLLEVBQUUsR0FBRyxNQUFNSixTQUN4Q1csSUFBSSxDQUFDLFNBQ0xDLE1BQU0sQ0FBRSxzRUFLUkMsRUFBRSxDQUFDLE1BQU1WLEtBQUtXLEVBQUUsRUFDaEJDLE1BQU07SUFFVCxJQUFJWCxPQUFPO1FBQ1RHLFFBQVFILEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU87SUFDVDtJQUVBLE9BQU9NO0FBQ1QsRUFBRTtBQUVGLHFCQUFxQjtBQUNkLE1BQU1TLFNBQVMsT0FBT0MsT0FBZUM7SUFDMUMsTUFBTSxFQUFFbkIsSUFBSSxFQUFFRSxLQUFLLEVBQUUsR0FBRyxNQUFNSixTQUFTSyxJQUFJLENBQUNpQixrQkFBa0IsQ0FBQztRQUM3REY7UUFDQUM7SUFDRjtJQUVBLElBQUlqQixPQUFPO1FBQ1QsTUFBTSxJQUFJbUIsTUFBTW5CLE1BQU1vQixPQUFPO0lBQy9CO0lBRUEsT0FBT3RCO0FBQ1QsRUFBRTtBQUVGLHFCQUFxQjtBQUNkLE1BQU11QixVQUFVO0lBQ3JCLE1BQU0sRUFBRXJCLEtBQUssRUFBRSxHQUFHLE1BQU1KLFNBQVNLLElBQUksQ0FBQ29CLE9BQU87SUFDN0MsSUFBSXJCLE9BQU87UUFDVCxNQUFNLElBQUltQixNQUFNbkIsTUFBTW9CLE9BQU87SUFDL0I7QUFDRixFQUFFO0FBRUYsMEJBQTBCO0FBQ25CLE1BQU1FLGFBQWEsT0FBT0M7SUFTL0Isa0NBQWtDO0lBQ2xDLE1BQU0sRUFBRXpCLE1BQU0wQixRQUFRLEVBQUV4QixPQUFPeUIsU0FBUyxFQUFFLEdBQUcsTUFBTTdCLFNBQVNLLElBQUksQ0FBQ3lCLE1BQU0sQ0FBQztRQUN0RVYsT0FBT08sU0FBU1AsS0FBSztRQUNyQkMsVUFBVU0sU0FBU04sUUFBUTtJQUM3QjtJQUVBLElBQUlRLFdBQVc7UUFDYixNQUFNLElBQUlOLE1BQU1NLFVBQVVMLE9BQU87SUFDbkM7SUFFQSxJQUFJLENBQUNJLFNBQVN6QixJQUFJLEVBQUU7UUFDbEIsTUFBTSxJQUFJb0IsTUFBTTtJQUNsQjtJQUVBLDRDQUE0QztJQUM1QyxNQUFNLEVBQUVyQixNQUFNUSxXQUFXLEVBQUVOLE9BQU8yQixZQUFZLEVBQUUsR0FBRyxNQUFNL0IsU0FDdERXLElBQUksQ0FBQyxTQUNMcUIsTUFBTSxDQUFDO1FBQ05sQixJQUFJYyxTQUFTekIsSUFBSSxDQUFDVyxFQUFFO1FBQ3BCTSxPQUFPTyxTQUFTUCxLQUFLO1FBQ3JCYSxXQUFXTixTQUFTTSxTQUFTO1FBQzdCQyxPQUFPUCxTQUFTTyxLQUFLO1FBQ3JCbEIsTUFBTVcsU0FBU1gsSUFBSTtRQUNuQm1CLFNBQVNSLFNBQVNRLE9BQU87UUFDekJDLFlBQVlULFNBQVNTLFVBQVU7SUFDakMsR0FDQ3hCLE1BQU0sR0FDTkcsTUFBTTtJQUVULElBQUlnQixjQUFjO1FBQ2hCLE1BQU0sSUFBSVIsTUFBTVEsYUFBYVAsT0FBTztJQUN0QztJQUVBLE9BQU87UUFBRUk7UUFBVWxCO0lBQVk7QUFDakMsRUFBRTtBQUVGLHFDQUFxQztBQUM5QixNQUFNMkIsb0JBQW9CLE9BQU9DO0lBQ3RDLE1BQU0sRUFBRXBDLElBQUksRUFBRUUsS0FBSyxFQUFFLEdBQUcsTUFBTUosU0FDM0JXLElBQUksQ0FBQyxTQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGNBQWN5QixXQUNqQnpCLEVBQUUsQ0FBQyxhQUFhLE1BQ2hCMEIsS0FBSyxDQUFDO0lBRVQsSUFBSW5DLE9BQU87UUFDVCxNQUFNLElBQUltQixNQUFNbkIsTUFBTW9CLE9BQU87SUFDL0I7SUFFQSxPQUFPdEI7QUFDVCxFQUFFO0FBRUYsK0JBQStCO0FBQ3hCLE1BQU1zQyxlQUFlO0lBQzFCLE1BQU0sRUFBRXRDLElBQUksRUFBRUUsS0FBSyxFQUFFLEdBQUcsTUFBTUosU0FDM0JXLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUMsS0FDUDJCLEtBQUssQ0FBQztJQUVULElBQUluQyxPQUFPO1FBQ1QsTUFBTSxJQUFJbUIsTUFBTW5CLE1BQU1vQixPQUFPO0lBQy9CO0lBRUEsT0FBT3RCO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL3N1cGFiYXNlLnRzPzA2ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcbmltcG9ydCB7IERhdGFiYXNlIH0gZnJvbSAnQC90eXBlcy9zdXBhYmFzZSc7XG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMITtcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZITtcblxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50PERhdGFiYXNlPihzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5KTtcblxuLy8g2K/Yp9mE2Kkg2YTZhNit2LXZiNmEINi52YTZiSDYp9mE2YXYs9iq2K7Yr9mFINin2YTYrdin2YTZilxuZXhwb3J0IGNvbnN0IGdldEN1cnJlbnRVc2VyID0gYXN5bmMgKCkgPT4ge1xuICBjb25zdCB7IGRhdGE6IHsgdXNlciB9LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XG4gIGlmIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgY3VycmVudCB1c2VyOicsIGVycm9yKTtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuICByZXR1cm4gdXNlcjtcbn07XG5cbi8vINiv2KfZhNipINmE2YTYqtit2YLZgiDZhdmGINi12YTYp9it2YrYp9iqINin2YTZhdiz2KrYrtiv2YVcbmV4cG9ydCBjb25zdCBjaGVja1VzZXJQZXJtaXNzaW9ucyA9IGFzeW5jIChyZXF1aXJlZFJvbGU6IHN0cmluZykgPT4ge1xuICBjb25zdCB1c2VyID0gYXdhaXQgZ2V0Q3VycmVudFVzZXIoKTtcbiAgaWYgKCF1c2VyKSByZXR1cm4gZmFsc2U7XG5cbiAgY29uc3QgeyBkYXRhOiB1c2VyUHJvZmlsZSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgLmZyb20oJ3VzZXJzJylcbiAgICAuc2VsZWN0KCdyb2xlLCBjYW1wX2lkLCBjb21wYW55X2lkJylcbiAgICAuZXEoJ2lkJywgdXNlci5pZClcbiAgICAuc2luZ2xlKCk7XG5cbiAgaWYgKGVycm9yIHx8ICF1c2VyUHJvZmlsZSkgcmV0dXJuIGZhbHNlO1xuXG4gIC8vINmF2YbYt9mCINin2YTYqtit2YLZgiDZhdmGINin2YTYtdmE2KfYrdmK2KfYqlxuICBzd2l0Y2ggKHJlcXVpcmVkUm9sZSkge1xuICAgIGNhc2UgJ2FkbWluJzpcbiAgICAgIHJldHVybiB1c2VyUHJvZmlsZS5yb2xlID09PSAnYWRtaW4nO1xuICAgIGNhc2UgJ2NhbXBfbWFuYWdlcic6XG4gICAgICByZXR1cm4gWydhZG1pbicsICdjYW1wX21hbmFnZXInXS5pbmNsdWRlcyh1c2VyUHJvZmlsZS5yb2xlKTtcbiAgICBjYXNlICdhY2NvdW50YW50JzpcbiAgICAgIHJldHVybiBbJ2FkbWluJywgJ2NhbXBfbWFuYWdlcicsICdhY2NvdW50YW50J10uaW5jbHVkZXModXNlclByb2ZpbGUucm9sZSk7XG4gICAgY2FzZSAnb3BlcmF0b3InOlxuICAgICAgcmV0dXJuIFsnYWRtaW4nLCAnY2FtcF9tYW5hZ2VyJywgJ2FjY291bnRhbnQnLCAnb3BlcmF0b3InXS5pbmNsdWRlcyh1c2VyUHJvZmlsZS5yb2xlKTtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIGZhbHNlO1xuICB9XG59O1xuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINmF2LnZhNmI2YXYp9iqINin2YTZhdiz2KrYrtiv2YUg2KfZhNmD2KfZhdmE2KlcbmV4cG9ydCBjb25zdCBnZXRVc2VyUHJvZmlsZSA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgdXNlciA9IGF3YWl0IGdldEN1cnJlbnRVc2VyKCk7XG4gIGlmICghdXNlcikgcmV0dXJuIG51bGw7XG5cbiAgY29uc3QgeyBkYXRhOiB1c2VyUHJvZmlsZSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgLmZyb20oJ3VzZXJzJylcbiAgICAuc2VsZWN0KGBcbiAgICAgICosXG4gICAgICBjYW1wOmNhbXBzKCopLFxuICAgICAgY29tcGFueTpjb21wYW5pZXMoKilcbiAgICBgKVxuICAgIC5lcSgnaWQnLCB1c2VyLmlkKVxuICAgIC5zaW5nbGUoKTtcblxuICBpZiAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXIgcHJvZmlsZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gdXNlclByb2ZpbGU7XG59O1xuXG4vLyDYr9in2YTYqSDZhNiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEXG5leHBvcnQgY29uc3Qgc2lnbkluID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgIGVtYWlsLFxuICAgIHBhc3N3b3JkLFxuICB9KTtcblxuICBpZiAoZXJyb3IpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IubWVzc2FnZSk7XG4gIH1cblxuICByZXR1cm4gZGF0YTtcbn07XG5cbi8vINiv2KfZhNipINmE2KrYs9is2YrZhCDYp9mE2K7YsdmI2KxcbmV4cG9ydCBjb25zdCBzaWduT3V0ID0gYXN5bmMgKCkgPT4ge1xuICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcbiAgaWYgKGVycm9yKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLm1lc3NhZ2UpO1xuICB9XG59O1xuXG4vLyDYr9in2YTYqSDZhNil2YbYtNin2KEg2YXYs9iq2K7Yr9mFINis2K/ZitivXG5leHBvcnQgY29uc3QgY3JlYXRlVXNlciA9IGFzeW5jICh1c2VyRGF0YToge1xuICBlbWFpbDogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBmdWxsX25hbWU6IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIHJvbGU6IHN0cmluZztcbiAgY2FtcF9pZD86IHN0cmluZztcbiAgY29tcGFueV9pZD86IHN0cmluZztcbn0pID0+IHtcbiAgLy8g2KXZhti02KfYoSDYp9mE2YXYs9iq2K7Yr9mFINmB2Yog2YbYuNin2YUg2KfZhNmF2LXYp9iv2YLYqVxuICBjb25zdCB7IGRhdGE6IGF1dGhEYXRhLCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25VcCh7XG4gICAgZW1haWw6IHVzZXJEYXRhLmVtYWlsLFxuICAgIHBhc3N3b3JkOiB1c2VyRGF0YS5wYXNzd29yZCxcbiAgfSk7XG5cbiAgaWYgKGF1dGhFcnJvcikge1xuICAgIHRocm93IG5ldyBFcnJvcihhdXRoRXJyb3IubWVzc2FnZSk7XG4gIH1cblxuICBpZiAoIWF1dGhEYXRhLnVzZXIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ9mB2LTZhCDZgdmKINil2YbYtNin2KEg2KfZhNmF2LPYqtiu2K/ZhScpO1xuICB9XG5cbiAgLy8g2KXYttin2YHYqSDYqNmK2KfZhtin2Kog2KfZhNmF2LPYqtiu2K/ZhSDYpdmE2Ykg2KzYr9mI2YQg2KfZhNmF2LPYqtiu2K/ZhdmK2YZcbiAgY29uc3QgeyBkYXRhOiB1c2VyUHJvZmlsZSwgZXJyb3I6IHByb2ZpbGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAuZnJvbSgndXNlcnMnKVxuICAgIC5pbnNlcnQoe1xuICAgICAgaWQ6IGF1dGhEYXRhLnVzZXIuaWQsXG4gICAgICBlbWFpbDogdXNlckRhdGEuZW1haWwsXG4gICAgICBmdWxsX25hbWU6IHVzZXJEYXRhLmZ1bGxfbmFtZSxcbiAgICAgIHBob25lOiB1c2VyRGF0YS5waG9uZSxcbiAgICAgIHJvbGU6IHVzZXJEYXRhLnJvbGUsXG4gICAgICBjYW1wX2lkOiB1c2VyRGF0YS5jYW1wX2lkLFxuICAgICAgY29tcGFueV9pZDogdXNlckRhdGEuY29tcGFueV9pZCxcbiAgICB9KVxuICAgIC5zZWxlY3QoKVxuICAgIC5zaW5nbGUoKTtcblxuICBpZiAocHJvZmlsZUVycm9yKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKHByb2ZpbGVFcnJvci5tZXNzYWdlKTtcbiAgfVxuXG4gIHJldHVybiB7IGF1dGhEYXRhLCB1c2VyUHJvZmlsZSB9O1xufTtcblxuLy8g2K/Yp9mE2Kkg2YTZhNit2LXZiNmEINi52YTZiSDYp9mE2YPZhdio2KfYqiDYrdiz2Kgg2KfZhNi02LHZg9ipXG5leHBvcnQgY29uc3QgZ2V0Q2FtcHNCeUNvbXBhbnkgPSBhc3luYyAoY29tcGFueUlkOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAuZnJvbSgnY2FtcHMnKVxuICAgIC5zZWxlY3QoJyonKVxuICAgIC5lcSgnY29tcGFueV9pZCcsIGNvbXBhbnlJZClcbiAgICAuZXEoJ2lzX2FjdGl2ZScsIHRydWUpXG4gICAgLm9yZGVyKCduYW1lJyk7XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLm1lc3NhZ2UpO1xuICB9XG5cbiAgcmV0dXJuIGRhdGE7XG59O1xuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINis2YXZiti5INin2YTYtNix2YPYp9iqXG5leHBvcnQgY29uc3QgZ2V0Q29tcGFuaWVzID0gYXN5bmMgKCkgPT4ge1xuICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdjb21wYW5pZXMnKVxuICAgIC5zZWxlY3QoJyonKVxuICAgIC5vcmRlcignbmFtZScpO1xuXG4gIGlmIChlcnJvcikge1xuICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5tZXNzYWdlKTtcbiAgfVxuXG4gIHJldHVybiBkYXRhO1xufTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUFub25LZXkiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsInN1cGFiYXNlIiwiZ2V0Q3VycmVudFVzZXIiLCJkYXRhIiwidXNlciIsImVycm9yIiwiYXV0aCIsImdldFVzZXIiLCJjb25zb2xlIiwiY2hlY2tVc2VyUGVybWlzc2lvbnMiLCJyZXF1aXJlZFJvbGUiLCJ1c2VyUHJvZmlsZSIsImZyb20iLCJzZWxlY3QiLCJlcSIsImlkIiwic2luZ2xlIiwicm9sZSIsImluY2x1ZGVzIiwiZ2V0VXNlclByb2ZpbGUiLCJzaWduSW4iLCJlbWFpbCIsInBhc3N3b3JkIiwic2lnbkluV2l0aFBhc3N3b3JkIiwiRXJyb3IiLCJtZXNzYWdlIiwic2lnbk91dCIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsImF1dGhEYXRhIiwiYXV0aEVycm9yIiwic2lnblVwIiwicHJvZmlsZUVycm9yIiwiaW5zZXJ0IiwiZnVsbF9uYW1lIiwicGhvbmUiLCJjYW1wX2lkIiwiY29tcGFueV9pZCIsImdldENhbXBzQnlDb21wYW55IiwiY29tcGFueUlkIiwib3JkZXIiLCJnZXRDb21wYW5pZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});