import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import '@/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'نظام إدارة سيارات الحمل',
  description: 'نظام إدارة عمليات دخول وخروج سيارات الحمل في الكمبات',
  keywords: 'إدارة سيارات، كمبات، سائقين، عمليات نقل',
  authors: [{ name: 'فريق التطوير' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'noindex, nofollow', // منع الفهرسة للأمان
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link
          href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.className} font-arabic`}>
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  );
}
