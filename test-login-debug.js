// اختبار مفصل لمشكلة تسجيل الدخول
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugLogin() {
  console.log('🔍 تشخيص مفصل لمشكلة تسجيل الدخول...\n');

  try {
    // 1. التحقق من الاتصال
    console.log('🔄 1. التحقق من الاتصال...');
    console.log('📡 Supabase URL:', supabaseUrl);
    console.log('🔑 API Key:', supabaseKey ? 'موجود' : 'مفقود');

    // 2. محاولة تسجيل الدخول
    console.log('\n🔄 2. محاولة تسجيل الدخول...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123',
    });

    if (loginError) {
      console.error('❌ فشل تسجيل الدخول:', loginError);
      console.log('\n🔧 حلول مقترحة:');
      if (loginError.message.includes('Invalid login credentials')) {
        console.log('• تأكد من إنشاء المستخدم في Supabase Auth');
        console.log('• تحقق من تأكيد البريد الإلكتروني');
      }
      return false;
    }

    console.log('✅ تم تسجيل الدخول بنجاح');
    console.log('👤 بيانات المستخدم:');
    console.log('   - ID:', loginData.user.id);
    console.log('   - Email:', loginData.user.email);
    console.log('   - Email Confirmed:', loginData.user.email_confirmed_at ? 'نعم' : 'لا');

    // 3. التحقق من الجلسة
    console.log('\n🔄 3. التحقق من الجلسة...');
    const { data: sessionData } = await supabase.auth.getSession();
    
    if (sessionData.session) {
      console.log('✅ الجلسة نشطة');
      console.log('   - Access Token:', sessionData.session.access_token ? 'موجود' : 'مفقود');
      console.log('   - Refresh Token:', sessionData.session.refresh_token ? 'موجود' : 'مفقود');
    } else {
      console.log('❌ لا توجد جلسة نشطة');
    }

    // 4. التحقق من بيانات المستخدم في جدول users
    console.log('\n🔄 4. التحقق من بيانات المستخدم...');
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select(`
        *,
        camp:camps(id, name),
        company:companies(id, name)
      `)
      .eq('id', loginData.user.id)
      .single();

    if (profileError) {
      console.error('❌ فشل في الحصول على بيانات المستخدم:', profileError);
      console.log('\n🔧 الحل:');
      console.log('شغل هذا الاستعلام في Supabase SQL Editor:');
      console.log(`INSERT INTO users (id, email, full_name, phone, role, company_id) VALUES ('${loginData.user.id}', '<EMAIL>', 'مدير النظام', '07901111111', 'admin', '550e8400-e29b-41d4-a716-446655440000');`);
      return false;
    }

    console.log('✅ تم الحصول على بيانات المستخدم');
    console.log('📋 تفاصيل المستخدم:');
    console.log('   - الاسم:', userProfile.full_name);
    console.log('   - الدور:', userProfile.role);
    console.log('   - الشركة:', userProfile.company?.name || 'غير محدد');
    console.log('   - الكمب:', userProfile.camp?.name || 'غير محدد');

    // 5. محاكاة عملية التوجيه
    console.log('\n🔄 5. محاكاة عملية التوجيه...');
    
    // تحديد الصفحة المناسبة حسب الدور
    let targetPage = '/dashboard';
    switch (userProfile.role) {
      case 'admin':
        targetPage = '/dashboard';
        console.log('🎯 الصفحة المستهدفة: لوحة تحكم المدير');
        break;
      case 'camp_manager':
        targetPage = '/dashboard';
        console.log('🎯 الصفحة المستهدفة: لوحة تحكم مدير الكمب');
        break;
      case 'accountant':
        targetPage = '/dashboard';
        console.log('🎯 الصفحة المستهدفة: لوحة تحكم المحاسب');
        break;
      case 'operator':
        targetPage = '/dashboard';
        console.log('🎯 الصفحة المستهدفة: لوحة تحكم العامل');
        break;
      default:
        console.log('⚠️ دور غير معروف:', userProfile.role);
    }

    console.log('✅ التوجيه يجب أن يعمل إلى:', targetPage);

    // 6. تسجيل الخروج للتنظيف
    console.log('\n🔄 6. تسجيل الخروج...');
    await supabase.auth.signOut();
    console.log('✅ تم تسجيل الخروج');

    return true;

  } catch (error) {
    console.error('\n❌ خطأ عام:', error);
    return false;
  }
}

// تشغيل التشخيص
debugLogin().then(success => {
  console.log('\n' + '='.repeat(60));
  if (success) {
    console.log('🎉 التشخيص مكتمل - المصادقة تعمل!');
    console.log('\n🔧 إذا كانت المشكلة ما زالت موجودة في المتصفح:');
    console.log('1. افتح Developer Tools (F12)');
    console.log('2. اذهب إلى Console');
    console.log('3. امسح الـ cache (Ctrl+Shift+R)');
    console.log('4. جرب تسجيل الدخول مرة أخرى');
    console.log('5. راقب الرسائل في Console');
    console.log('\n📋 خطوات إضافية:');
    console.log('• تأكد من أن الخادم يعمل على localhost:3000');
    console.log('• جرب في نافذة خاصة (Incognito)');
    console.log('• تحقق من أن JavaScript مفعل');
  } else {
    console.log('❌ فشل التشخيص - هناك مشكلة في المصادقة');
    console.log('\n🔧 خطوات الإصلاح:');
    console.log('1. تحقق من إعدادات Supabase');
    console.log('2. تأكد من إنشاء المستخدم');
    console.log('3. شغل البيانات التجريبية');
  }
  console.log('='.repeat(60));
  
  process.exit(success ? 0 : 1);
});
