"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Layout from "@/components/Layout";
import {
  getDrivers,
  createDriver,
  updateDriver,
  deleteDriver,
} from "@/lib/drivers";
import { getCampsByCompany } from "@/lib/supabase";
import { generateDriverQRCode } from "@/lib/qr-generator";
import { Driver, CreateDriverData, Camp } from "@/types/database";

export default function DriversPage() {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [camps, setCamps] = useState<Camp[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [qrCodeImage, setQrCodeImage] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCamp, setSelectedCamp] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Form state
  const [formData, setFormData] = useState<CreateDriverData>({
    name: "",
    phone: "",
    vehicle_type: "",
    vehicle_number: "",
    national_id: "",
    notes: "",
    camp_id: "",
  });

  useEffect(() => {
    loadDrivers();
    loadCamps();
  }, [currentPage, searchTerm, selectedCamp]);

  const loadDrivers = async () => {
    try {
      setIsLoading(true);
      const response = await getDrivers(
        selectedCamp || undefined,
        currentPage,
        10,
        searchTerm || undefined,
        true
      );
      setDrivers(response.data);
      setTotalPages(response.total_pages);
    } catch (error) {
      console.error("Error loading drivers:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCamps = async () => {
    try {
      // TODO: Get user's company ID from context
      const companyId = "550e8400-e29b-41d4-a716-446655440000"; // Mock company ID
      const campsData = await getCampsByCompany(companyId);
      setCamps(campsData);
      if (campsData.length > 0 && !formData.camp_id) {
        setFormData((prev) => ({ ...prev, camp_id: campsData[0].id }));
      }
    } catch (error) {
      console.error("Error loading camps:", error);
    }
  };

  const handleAddDriver = async () => {
    try {
      if (
        !formData.name ||
        !formData.phone ||
        !formData.vehicle_number ||
        !formData.camp_id
      ) {
        alert("يرجى ملء جميع الحقول المطلوبة");
        return;
      }

      await createDriver(formData);
      setShowAddModal(false);
      setFormData({
        name: "",
        phone: "",
        vehicle_type: "",
        vehicle_number: "",
        national_id: "",
        notes: "",
        camp_id: camps[0]?.id || "",
      });
      loadDrivers();
    } catch (error) {
      console.error("Error adding driver:", error);
      alert("فشل في إضافة السائق");
    }
  };

  const handleShowQR = async (driver: Driver) => {
    try {
      const qrImage = await generateDriverQRCode(driver);
      setQrCodeImage(qrImage);
      setSelectedDriver(driver);
      setShowQRModal(true);
    } catch (error) {
      console.error("Error generating QR code:", error);
      alert("فشل في توليد رمز QR");
    }
  };

  const handleDeleteDriver = async (driver: Driver) => {
    if (confirm(`هل أنت متأكد من حذف السائق: ${driver.name}؟`)) {
      try {
        await deleteDriver(driver.id);
        loadDrivers();
      } catch (error) {
        console.error("Error deleting driver:", error);
        alert("فشل في حذف السائق");
      }
    }
  };

  const downloadQRCode = () => {
    if (qrCodeImage && selectedDriver) {
      const link = document.createElement("a");
      link.download = `qr-${selectedDriver.name}-${selectedDriver.vehicle_number}.png`;
      link.href = qrCodeImage;
      link.click();
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة السائقين</h1>
            <p className="mt-1 text-sm text-gray-600">
              إضافة وإدارة بيانات السائقين مع رموز QR
            </p>
          </div>
          <button onClick={() => setShowAddModal(true)} className="btn-primary">
            إضافة سائق جديد
          </button>
        </div>

        {/* Filters */}
        <div className="card">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                البحث
              </label>
              <input
                type="text"
                placeholder="البحث بالاسم، الهاتف، أو رقم السيارة"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الكمب
              </label>
              <select
                value={selectedCamp}
                onChange={(e) => setSelectedCamp(e.target.value)}
                className="input-field"
              >
                <option value="">جميع الكمبات</option>
                {camps.map((camp) => (
                  <option key={camp.id} value={camp.id}>
                    {camp.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCamp("");
                  setCurrentPage(1);
                }}
                className="btn-secondary w-full"
              >
                إعادة تعيين
              </button>
            </div>
          </div>
        </div>

        {/* Drivers Table */}
        <div className="card">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <div className="table-container">
              <table className="table">
                <thead>
                  <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>نوع السيارة</th>
                    <th>رقم السيارة</th>
                    <th>الكمب</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {drivers.map((driver) => (
                    <tr key={driver.id}>
                      <td className="font-medium">{driver.name}</td>
                      <td>{driver.phone}</td>
                      <td>{driver.vehicle_type}</td>
                      <td>{driver.vehicle_number}</td>
                      <td>{driver.camp?.name}</td>
                      <td>
                        <div className="flex space-x-2 space-x-reverse">
                          <button
                            onClick={() => handleShowQR(driver)}
                            className="text-primary-600 hover:text-primary-900"
                            title="عرض QR Code"
                          >
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                              />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteDriver(driver)}
                            className="text-danger-600 hover:text-danger-900"
                            title="حذف"
                          >
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {drivers.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">لا توجد سائقين</p>
                </div>
              )}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2 space-x-reverse">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="btn-secondary disabled:opacity-50"
                >
                  السابق
                </button>
                <span className="px-4 py-2 text-sm text-gray-700">
                  صفحة {currentPage} من {totalPages}
                </span>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="btn-secondary disabled:opacity-50"
                >
                  التالي
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Driver Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                إضافة سائق جديد
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الاسم *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    className="input-field"
                    placeholder="اسم السائق"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم الهاتف *
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        phone: e.target.value,
                      }))
                    }
                    className="input-field"
                    placeholder="07xxxxxxxxx"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    نوع السيارة
                  </label>
                  <input
                    type="text"
                    value={formData.vehicle_type}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        vehicle_type: e.target.value,
                      }))
                    }
                    className="input-field"
                    placeholder="شاحنة كبيرة، شاحنة متوسطة، إلخ"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم السيارة *
                  </label>
                  <input
                    type="text"
                    value={formData.vehicle_number}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        vehicle_number: e.target.value,
                      }))
                    }
                    className="input-field"
                    placeholder="بغداد 12345"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الكمب *
                  </label>
                  <select
                    value={formData.camp_id}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        camp_id: e.target.value,
                      }))
                    }
                    className="input-field"
                  >
                    <option value="">اختر الكمب</option>
                    {camps.map((camp) => (
                      <option key={camp.id} value={camp.id}>
                        {camp.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم الهوية
                  </label>
                  <input
                    type="text"
                    value={formData.national_id}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        national_id: e.target.value,
                      }))
                    }
                    className="input-field"
                    placeholder="رقم الهوية الوطنية"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        notes: e.target.value,
                      }))
                    }
                    rows={3}
                    className="input-field"
                    placeholder="ملاحظات إضافية"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 space-x-reverse mt-6">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="btn-secondary"
                >
                  إلغاء
                </button>
                <button onClick={handleAddDriver} className="btn-primary">
                  إضافة السائق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* QR Code Modal */}
      {showQRModal && selectedDriver && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                رمز QR للسائق: {selectedDriver.name}
              </h3>

              {qrCodeImage && (
                <div className="mb-4">
                  <Image
                    src={qrCodeImage}
                    alt="QR Code"
                    width={256}
                    height={256}
                    className="mx-auto border rounded-lg"
                  />
                </div>
              )}

              <div className="text-sm text-gray-600 mb-4">
                <p>رقم السيارة: {selectedDriver.vehicle_number}</p>
                <p>الهاتف: {selectedDriver.phone}</p>
              </div>

              <div className="flex justify-center space-x-3 space-x-reverse">
                <button
                  onClick={() => setShowQRModal(false)}
                  className="btn-secondary"
                >
                  إغلاق
                </button>
                <button onClick={downloadQRCode} className="btn-primary">
                  تحميل الصورة
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}
