'use client';

import { useState, useRef, useEffect } from 'react';
import QrScanner from 'qr-scanner';

interface QRScannerProps {
  onScan: (result: string) => void;
  onError?: (error: string) => void;
  isActive: boolean;
}

export default function QRScanner({ onScan, onError, isActive }: QRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [qrScanner, setQrScanner] = useState<QrScanner | null>(null);
  const [hasCamera, setHasCamera] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isActive && videoRef.current) {
      startScanner();
    } else if (qrScanner) {
      stopScanner();
    }

    return () => {
      if (qrScanner) {
        qrScanner.destroy();
      }
    };
  }, [isActive]);

  const startScanner = async () => {
    if (!videoRef.current) return;

    setIsLoading(true);
    
    try {
      // التحقق من وجود كاميرا
      const hasCamera = await QrScanner.hasCamera();
      if (!hasCamera) {
        setHasCamera(false);
        onError?.('لا توجد كاميرا متاحة');
        setIsLoading(false);
        return;
      }

      const scanner = new QrScanner(
        videoRef.current,
        (result) => {
          onScan(result.data);
        },
        {
          onDecodeError: (error) => {
            // تجاهل أخطاء فك التشفير العادية
            console.log('QR decode error:', error);
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: 'environment', // استخدام الكاميرا الخلفية إذا كانت متاحة
        }
      );

      await scanner.start();
      setQrScanner(scanner);
      setIsLoading(false);
    } catch (error) {
      console.error('Error starting QR scanner:', error);
      onError?.('فشل في تشغيل الكاميرا');
      setIsLoading(false);
    }
  };

  const stopScanner = () => {
    if (qrScanner) {
      qrScanner.stop();
      qrScanner.destroy();
      setQrScanner(null);
    }
  };

  const switchCamera = async () => {
    if (!qrScanner) return;

    try {
      const cameras = await QrScanner.listCameras(true);
      if (cameras.length > 1) {
        const currentCamera = await qrScanner.getCamera();
        const currentIndex = cameras.findIndex(camera => camera.id === currentCamera?.id);
        const nextIndex = (currentIndex + 1) % cameras.length;
        await qrScanner.setCamera(cameras[nextIndex].id);
      }
    } catch (error) {
      console.error('Error switching camera:', error);
      onError?.('فشل في تبديل الكاميرا');
    }
  };

  if (!hasCamera) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-gray-100 rounded-lg">
        <svg className="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <p className="text-gray-600 text-center">
          لا توجد كاميرا متاحة
          <br />
          يرجى التأكد من وجود كاميرا والسماح بالوصول إليها
        </p>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="relative bg-black rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          className="w-full h-64 sm:h-80 object-cover"
          playsInline
          muted
        />
        
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-center text-white">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <p>جاري تشغيل الكاميرا...</p>
            </div>
          </div>
        )}

        {/* إطار المسح */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="w-48 h-48 border-2 border-white border-dashed rounded-lg flex items-center justify-center">
            <div className="w-40 h-40 border-2 border-primary-500 rounded-lg"></div>
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="absolute bottom-4 left-4 right-4 flex justify-center space-x-4 space-x-reverse">
          <button
            onClick={switchCamera}
            className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-colors"
            title="تبديل الكاميرا"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      {/* تعليمات الاستخدام */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-500 mt-0.5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">تعليمات المسح:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>وجه الكاميرا نحو رمز QR الخاص بالسائق</li>
              <li>تأكد من وضوح الرمز داخل الإطار</li>
              <li>حافظ على ثبات الجهاز لبضع ثوان</li>
              <li>سيتم المسح تلقائياً عند اكتشاف الرمز</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
