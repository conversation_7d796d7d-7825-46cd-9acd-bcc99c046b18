'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function TestRedirectPage() {
  const [method, setMethod] = useState('');
  const [status, setStatus] = useState('');
  const router = useRouter();

  const testRedirect = (redirectMethod: string) => {
    setMethod(redirectMethod);
    setStatus('جاري التوجيه...');

    switch (redirectMethod) {
      case 'router.push':
        router.push('/dashboard');
        setTimeout(() => {
          if (window.location.pathname === '/test-redirect') {
            setStatus('فشل - لم يتم التوجيه');
          }
        }, 2000);
        break;

      case 'router.replace':
        router.replace('/dashboard');
        setTimeout(() => {
          if (window.location.pathname === '/test-redirect') {
            setStatus('فشل - لم يتم التوجيه');
          }
        }, 2000);
        break;

      case 'window.location.href':
        window.location.href = '/dashboard';
        break;

      case 'window.location.replace':
        window.location.replace('/dashboard');
        break;

      case 'window.location.assign':
        window.location.assign('/dashboard');
        break;

      default:
        setStatus('طريقة غير معروفة');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            اختبار طرق التوجيه
          </h1>

          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 text-blue-700 p-4 rounded-lg">
              <p><strong>الهدف:</strong> اختبار طرق مختلفة للتوجيه إلى /dashboard</p>
              <p><strong>الطريقة المستخدمة:</strong> {method || 'لم يتم الاختبار بعد'}</p>
              <p><strong>الحالة:</strong> {status || 'جاهز للاختبار'}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => testRedirect('router.push')}
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                اختبار router.push()
              </button>

              <button
                onClick={() => testRedirect('router.replace')}
                className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                اختبار router.replace()
              </button>

              <button
                onClick={() => testRedirect('window.location.href')}
                className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                اختبار window.location.href
              </button>

              <button
                onClick={() => testRedirect('window.location.replace')}
                className="bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                اختبار window.location.replace()
              </button>

              <button
                onClick={() => testRedirect('window.location.assign')}
                className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                اختبار window.location.assign()
              </button>

              <button
                onClick={() => {
                  setMethod('');
                  setStatus('');
                }}
                className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                إعادة تعيين
              </button>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 rounded-lg">
              <p><strong>ملاحظات:</strong></p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>router.push/replace: طرق Next.js للتوجيه</li>
                <li>window.location.*: طرق المتصفح للتوجيه</li>
                <li>إذا لم تعمل طرق Next.js، استخدم طرق المتصفح</li>
                <li>افتح Developer Tools لرؤية أي أخطاء</li>
              </ul>
            </div>

            <div className="text-center">
              <a
                href="/login"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                العودة إلى صفحة تسجيل الدخول
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
