import { createClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// دالة للحصول على المستخدم الحالي
export const getCurrentUser = async () => {
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();
  if (error) {
    console.error("Error getting current user:", error);
    return null;
  }
  return user;
};

// دالة للتحقق من صلاحيات المستخدم
export const checkUserPermissions = async (requiredRole: string) => {
  const user = await getCurrentUser();
  if (!user) return false;

  const { data: userProfile, error } = await supabase
    .from("users")
    .select("role, camp_id, company_id")
    .eq("id", user.id)
    .single();

  if (error || !userProfile) return false;

  // منطق التحقق من الصلاحيات
  switch (requiredRole) {
    case "admin":
      return userProfile.role === "admin";
    case "camp_manager":
      return ["admin", "camp_manager"].includes(userProfile.role);
    case "accountant":
      return ["admin", "camp_manager", "accountant"].includes(userProfile.role);
    case "operator":
      return ["admin", "camp_manager", "accountant", "operator"].includes(
        userProfile.role
      );
    default:
      return false;
  }
};

// دالة للحصول على معلومات المستخدم الكاملة
export const getUserProfile = async () => {
  const user = await getCurrentUser();
  if (!user) return null;

  const { data: userProfile, error } = await supabase
    .from("users")
    .select(
      `
      *,
      camp:camps(*),
      company:companies(*)
    `
    )
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Error getting user profile:", error);
    return null;
  }

  return userProfile;
};

// دالة لتسجيل الدخول
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

// دالة لتسجيل الخروج
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) {
    throw new Error(error.message);
  }
};

// دالة لإنشاء مستخدم جديد
export const createUser = async (userData: {
  email: string;
  password: string;
  full_name: string;
  phone?: string;
  role: string;
  camp_id?: string;
  company_id?: string;
}) => {
  // إنشاء المستخدم في نظام المصادقة
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email: userData.email,
    password: userData.password,
  });

  if (authError) {
    throw new Error(authError.message);
  }

  if (!authData.user) {
    throw new Error("فشل في إنشاء المستخدم");
  }

  // إضافة بيانات المستخدم إلى جدول المستخدمين
  const { data: userProfile, error: profileError } = await supabase
    .from("users")
    .insert({
      id: authData.user.id,
      email: userData.email,
      full_name: userData.full_name,
      phone: userData.phone,
      role: userData.role,
      camp_id: userData.camp_id,
      company_id: userData.company_id,
    })
    .select()
    .single();

  if (profileError) {
    throw new Error(profileError.message);
  }

  return userProfile;
};

// دالة للحصول على الكمبات حسب الشركة
export const getCampsByCompany = async (companyId: string) => {
  const { data, error } = await supabase
    .from("camps")
    .select("*")
    .eq("company_id", companyId)
    .eq("is_active", true)
    .order("name");

  if (error) {
    throw new Error(error.message);
  }

  return (data || []) as any[];
};

// دالة للحصول على جميع الشركات
export const getCompanies = async () => {
  const { data, error } = await supabase
    .from("companies")
    .select("*")
    .order("name");

  if (error) {
    throw new Error(error.message);
  }

  return data;
};
