// اختبار تسجيل الدخول
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testLogin() {
  console.log('🔍 اختبار تسجيل الدخول...\n');

  const email = '<EMAIL>';
  const password = 'password123';

  try {
    // اختبار 1: التحقق من وجود المستخدم في auth.users
    console.log('🔄 التحقق من وجود المستخدم في auth.users...');
    const { data: authUsers, error: authError } = await supabase
      .from('auth.users')
      .select('id, email, email_confirmed_at')
      .eq('email', email);

    if (authError) {
      console.log('⚠️ لا يمكن الوصول لجدول auth.users مباشرة (هذا طبيعي)');
    } else {
      console.log('✅ المستخدم موجود في auth.users:', authUsers);
    }

    // اختبار 2: محاولة تسجيل الدخول
    console.log('\n🔄 محاولة تسجيل الدخول...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (loginError) {
      console.error('❌ فشل تسجيل الدخول:', loginError.message);
      
      // اقتراحات للحل
      console.log('\n🔧 حلول مقترحة:');
      if (loginError.message.includes('Invalid login credentials')) {
        console.log('1. تأكد من إنشاء المستخدم في Supabase Auth');
        console.log('2. تأكد من تأكيد البريد الإلكتروني (Email Confirm)');
        console.log('3. تحقق من كلمة المرور');
      }
      if (loginError.message.includes('Email not confirmed')) {
        console.log('1. اذهب إلى Authentication > Users في Supabase');
        console.log('2. ابحث عن المستخدم وانقر على "..."');
        console.log('3. اختر "Send confirmation email" أو قم بتأكيد البريد يدوياً');
      }
      
      return false;
    }

    console.log('✅ تم تسجيل الدخول بنجاح!');
    console.log('👤 بيانات المستخدم:', {
      id: loginData.user?.id,
      email: loginData.user?.email,
      confirmed_at: loginData.user?.email_confirmed_at
    });

    // اختبار 3: التحقق من وجود المستخدم في جدول users
    console.log('\n🔄 التحقق من وجود المستخدم في جدول users...');
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', loginData.user.id)
      .single();

    if (profileError) {
      console.error('❌ المستخدم غير موجود في جدول users:', profileError.message);
      console.log('\n🔧 الحل:');
      console.log('شغل هذا الاستعلام في SQL Editor:');
      console.log(`INSERT INTO users (id, email, full_name, phone, role, company_id) VALUES ('${loginData.user.id}', '${email}', 'مدير النظام', '07901111111', 'admin', '550e8400-e29b-41d4-a716-446655440000');`);
      return false;
    }

    console.log('✅ المستخدم موجود في جدول users:');
    console.log('📋 بيانات الملف الشخصي:', {
      name: userProfile.full_name,
      role: userProfile.role,
      email: userProfile.email
    });

    // اختبار 4: تسجيل الخروج
    console.log('\n🔄 تسجيل الخروج...');
    const { error: signOutError } = await supabase.auth.signOut();
    
    if (signOutError) {
      console.error('❌ خطأ في تسجيل الخروج:', signOutError.message);
    } else {
      console.log('✅ تم تسجيل الخروج بنجاح');
    }

    return true;

  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
    return false;
  }
}

// تشغيل الاختبار
testLogin().then(success => {
  if (success) {
    console.log('\n🎉 اختبار تسجيل الدخول مكتمل بنجاح!');
    console.log('\n📋 يمكنك الآن:');
    console.log('1. تشغيل الخادم: npm run dev');
    console.log('2. الذهاب إلى: http://localhost:3000/login');
    console.log('3. تسجيل الدخول بـ: <EMAIL> / password123');
  } else {
    console.log('\n❌ فشل اختبار تسجيل الدخول!');
    console.log('\n🔧 خطوات الإصلاح:');
    console.log('1. تأكد من إنشاء المستخدم في Supabase Auth');
    console.log('2. تأكد من تأكيد البريد الإلكتروني');
    console.log('3. أضف بيانات المستخدم في جدول users');
  }
  
  process.exit(success ? 0 : 1);
});
