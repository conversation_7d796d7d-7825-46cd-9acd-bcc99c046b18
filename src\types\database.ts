// أنواع البيانات للنظام

export interface Company {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface Camp {
  id: string;
  company_id: string;
  name: string;
  location: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  company?: Company;
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  role: UserRole;
  camp_id?: string;
  company_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  camp?: Camp;
  company?: Company;
}

export type UserRole = 'admin' | 'camp_manager' | 'accountant' | 'operator';

export interface Driver {
  id: string;
  camp_id: string;
  name: string;
  phone: string;
  vehicle_type: string;
  vehicle_number: string;
  national_id?: string;
  notes?: string;
  qr_code: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  camp?: Camp;
}

export interface Material {
  id: string;
  name: string;
  description?: string;
  unit_price: number;
  unit: string; // طن، متر مكعب، إلخ
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Operation {
  id: string;
  camp_id: string;
  driver_id: string;
  material_id: string;
  operation_type: OperationType;
  quantity: number;
  unit_price: number;
  total_amount: number;
  notes?: string;
  operator_id: string;
  operation_date: string;
  created_at: string;
  updated_at: string;
  camp?: Camp;
  driver?: Driver;
  material?: Material;
  operator?: User;
}

export type OperationType = 'entry' | 'exit';

// أنواع للتقارير والإحصائيات
export interface DashboardStats {
  total_operations_today: number;
  total_amount_today: number;
  active_drivers: number;
  total_operations_month: number;
  total_amount_month: number;
  operations_by_material: {
    material_name: string;
    count: number;
    total_amount: number;
  }[];
  operations_by_type: {
    type: OperationType;
    count: number;
    total_amount: number;
  }[];
}

export interface ReportFilter {
  camp_id?: string;
  driver_id?: string;
  material_id?: string;
  operation_type?: OperationType;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// أنواع للنماذج
export interface CreateDriverData {
  name: string;
  phone: string;
  vehicle_type: string;
  vehicle_number: string;
  national_id?: string;
  notes?: string;
  camp_id: string;
}

export interface CreateOperationData {
  driver_id: string;
  material_id: string;
  operation_type: OperationType;
  quantity: number;
  notes?: string;
  camp_id: string;
}

export interface UpdateDriverData extends Partial<CreateDriverData> {
  is_active?: boolean;
}

// أنواع للمصادقة
export interface AuthUser {
  id: string;
  email: string;
  full_name: string;
  role: UserRole;
  camp_id?: string;
  company_id?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  full_name: string;
  phone?: string;
  role: UserRole;
  camp_id?: string;
  company_id?: string;
}
