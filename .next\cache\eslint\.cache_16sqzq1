[{"D:\\شركة النرجس\\car management\\src\\app\\dashboard\\drivers\\page.tsx": "1", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\operations\\page.tsx": "2", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\page.tsx": "3", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\reports\\page.tsx": "4", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\scanner\\page.tsx": "5", "D:\\شركة النرجس\\car management\\src\\app\\error.tsx": "6", "D:\\شركة النرجس\\car management\\src\\app\\layout.tsx": "7", "D:\\شركة النرجس\\car management\\src\\app\\loading.tsx": "8", "D:\\شركة النرجس\\car management\\src\\app\\login\\page.tsx": "9", "D:\\شركة النرجس\\car management\\src\\app\\not-found.tsx": "10", "D:\\شركة النرجس\\car management\\src\\app\\page.tsx": "11", "D:\\شركة النرجس\\car management\\src\\components\\Layout.tsx": "12", "D:\\شركة النرجس\\car management\\src\\components\\QRScanner.tsx": "13", "D:\\شركة النرجس\\car management\\src\\lib\\drivers.ts": "14", "D:\\شركة النرجس\\car management\\src\\lib\\operations.ts": "15", "D:\\شركة النرجس\\car management\\src\\lib\\pdf-generator.ts": "16", "D:\\شركة النرجس\\car management\\src\\lib\\qr-generator.ts": "17", "D:\\شركة النرجس\\car management\\src\\lib\\supabase.ts": "18", "D:\\شركة النرجس\\car management\\src\\middleware.ts": "19", "D:\\شركة النرجس\\car management\\src\\types\\database.ts": "20", "D:\\شركة النرجس\\car management\\src\\types\\supabase.ts": "21"}, {"size": 18094, "mtime": 1749655746727, "results": "22", "hashOfConfig": "23"}, {"size": 12650, "mtime": 1749654117585, "results": "24", "hashOfConfig": "23"}, {"size": 10781, "mtime": 1749653424030, "results": "25", "hashOfConfig": "23"}, {"size": 16170, "mtime": 1749654192943, "results": "26", "hashOfConfig": "23"}, {"size": 14544, "mtime": 1749653891757, "results": "27", "hashOfConfig": "23"}, {"size": 2371, "mtime": 1749654499504, "results": "28", "hashOfConfig": "23"}, {"size": 1418, "mtime": 1749653242703, "results": "29", "hashOfConfig": "23"}, {"size": 372, "mtime": 1749654507360, "results": "30", "hashOfConfig": "23"}, {"size": 5150, "mtime": 1749653319927, "results": "31", "hashOfConfig": "23"}, {"size": 1751, "mtime": 1749654522163, "results": "32", "hashOfConfig": "23"}, {"size": 10474, "mtime": 1749655797814, "results": "33", "hashOfConfig": "23"}, {"size": 10124, "mtime": 1749655844627, "results": "34", "hashOfConfig": "23"}, {"size": 6335, "mtime": 1749653704700, "results": "35", "hashOfConfig": "23"}, {"size": 8789, "mtime": 1749653771719, "results": "36", "hashOfConfig": "23"}, {"size": 10592, "mtime": 1749653814527, "results": "37", "hashOfConfig": "23"}, {"size": 10308, "mtime": 1749654242256, "results": "38", "hashOfConfig": "23"}, {"size": 5314, "mtime": 1749653734304, "results": "39", "hashOfConfig": "23"}, {"size": 4230, "mtime": 1749653222670, "results": "40", "hashOfConfig": "23"}, {"size": 1567, "mtime": 1749654303454, "results": "41", "hashOfConfig": "23"}, {"size": 3481, "mtime": 1749655593598, "results": "42", "hashOfConfig": "23"}, {"size": 7580, "mtime": 1749653455870, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1n9atck", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\drivers\\page.tsx", ["107", "108"], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\operations\\page.tsx", ["109"], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\page.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\reports\\page.tsx", ["110"], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\scanner\\page.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\error.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\layout.tsx", ["111", "112"], [], "D:\\شركة النرجس\\car management\\src\\app\\loading.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\login\\page.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\not-found.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\page.tsx", ["113"], [], "D:\\شركة النرجس\\car management\\src\\components\\Layout.tsx", ["114"], [], "D:\\شركة النرجس\\car management\\src\\components\\QRScanner.tsx", ["115"], [], "D:\\شركة النرجس\\car management\\src\\lib\\drivers.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\operations.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\pdf-generator.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\qr-generator.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\supabase.ts", [], [], "D:\\شركة النرجس\\car management\\src\\middleware.ts", [], [], "D:\\شركة النرجس\\car management\\src\\types\\database.ts", [], [], "D:\\شركة النرجس\\car management\\src\\types\\supabase.ts", [], [], {"ruleId": "116", "severity": 1, "message": "117", "line": 42, "column": 6, "nodeType": "118", "endLine": 42, "endColumn": 45, "suggestions": "119"}, {"ruleId": "120", "severity": 1, "message": "121", "line": 482, "column": 19, "nodeType": "122", "endLine": 486, "endColumn": 21}, {"ruleId": "116", "severity": 1, "message": "123", "line": 31, "column": 6, "nodeType": "118", "endLine": 31, "endColumn": 15, "suggestions": "124"}, {"ruleId": "116", "severity": 1, "message": "125", "line": 46, "column": 6, "nodeType": "118", "endLine": 46, "endColumn": 15, "suggestions": "126"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 26, "column": 9, "nodeType": "122", "endLine": 29, "endColumn": 11}, {"ruleId": "127", "severity": 1, "message": "128", "line": 30, "column": 9, "nodeType": "122", "endLine": 33, "endColumn": 11}, {"ruleId": "116", "severity": 1, "message": "129", "line": 15, "column": 6, "nodeType": "118", "endLine": 15, "endColumn": 8, "suggestions": "130"}, {"ruleId": "116", "severity": 1, "message": "131", "line": 22, "column": 6, "nodeType": "118", "endLine": 22, "endColumn": 8, "suggestions": "132"}, {"ruleId": "116", "severity": 1, "message": "133", "line": 30, "column": 6, "nodeType": "118", "endLine": 30, "endColumn": 16, "suggestions": "134"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadCamps' and 'loadDrivers'. Either include them or remove the dependency array.", "ArrayExpression", ["135"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'loadOperations'. Either include it or remove the dependency array.", ["136"], "React Hook useEffect has a missing dependency: 'loadReportData'. Either include it or remove the dependency array.", ["137"], "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "React Hook useEffect has a missing dependency: 'checkUser'. Either include it or remove the dependency array.", ["138"], "React Hook useEffect has a missing dependency: 'loadUser'. Either include it or remove the dependency array.", ["139"], "React Hook useEffect has missing dependencies: 'qrScanner', 'startScanner', and 'stopScanner'. Either include them or remove the dependency array.", ["140"], {"desc": "141", "fix": "142"}, {"desc": "143", "fix": "144"}, {"desc": "145", "fix": "146"}, {"desc": "147", "fix": "148"}, {"desc": "149", "fix": "150"}, {"desc": "151", "fix": "152"}, "Update the dependencies array to be: [currentPage, loadCamps, loadDrivers, searchTerm, selectedCamp]", {"range": "153", "text": "154"}, "Update the dependencies array to be: [filters, loadOperations]", {"range": "155", "text": "156"}, "Update the dependencies array to be: [filters, loadReportData]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [checkUser]", {"range": "159", "text": "160"}, "Update the dependencies array to be: [loadUser]", {"range": "161", "text": "162"}, "Update the dependencies array to be: [isActive, qrScanner, startScanner, stopScanner]", {"range": "163", "text": "164"}, [1325, 1364], "[currentPage, loadCamps, loadDrivers, searchTerm, selectedCamp]", [1048, 1057], "[filters, loadOperations]", [1471, 1480], "[filters, loadReportData]", [394, 396], "[checkUser]", [646, 648], "[loadUser]", [768, 778], "[isActive, qrScanner, startScanner, stopScanner]"]