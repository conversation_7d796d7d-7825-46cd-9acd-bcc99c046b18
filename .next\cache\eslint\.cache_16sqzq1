[{"D:\\شركة النرجس\\car management\\src\\app\\dashboard\\drivers\\page.tsx": "1", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\operations\\page.tsx": "2", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\page.tsx": "3", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\reports\\page.tsx": "4", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\scanner\\page.tsx": "5", "D:\\شركة النرجس\\car management\\src\\app\\error.tsx": "6", "D:\\شركة النرجس\\car management\\src\\app\\layout.tsx": "7", "D:\\شركة النرجس\\car management\\src\\app\\loading.tsx": "8", "D:\\شركة النرجس\\car management\\src\\app\\login\\page.tsx": "9", "D:\\شركة النرجس\\car management\\src\\app\\not-found.tsx": "10", "D:\\شركة النرجس\\car management\\src\\app\\page.tsx": "11", "D:\\شركة النرجس\\car management\\src\\components\\Layout.tsx": "12", "D:\\شركة النرجس\\car management\\src\\components\\QRScanner.tsx": "13", "D:\\شركة النرجس\\car management\\src\\lib\\drivers.ts": "14", "D:\\شركة النرجس\\car management\\src\\lib\\operations.ts": "15", "D:\\شركة النرجس\\car management\\src\\lib\\pdf-generator.ts": "16", "D:\\شركة النرجس\\car management\\src\\lib\\qr-generator.ts": "17", "D:\\شركة النرجس\\car management\\src\\lib\\supabase.ts": "18", "D:\\شركة النرجس\\car management\\src\\middleware.ts": "19", "D:\\شركة النرجس\\car management\\src\\types\\database.ts": "20", "D:\\شركة النرجس\\car management\\src\\types\\supabase.ts": "21", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\camps\\page.tsx": "22", "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\users\\page.tsx": "23"}, {"size": 18082, "mtime": 1749655901615, "results": "24", "hashOfConfig": "25"}, {"size": 12650, "mtime": 1749654117585, "results": "26", "hashOfConfig": "25"}, {"size": 12857, "mtime": 1749657686061, "results": "27", "hashOfConfig": "25"}, {"size": 16170, "mtime": 1749654192943, "results": "28", "hashOfConfig": "25"}, {"size": 14544, "mtime": 1749653891757, "results": "29", "hashOfConfig": "25"}, {"size": 2371, "mtime": 1749654499504, "results": "30", "hashOfConfig": "25"}, {"size": 1418, "mtime": 1749653242703, "results": "31", "hashOfConfig": "25"}, {"size": 372, "mtime": 1749654507360, "results": "32", "hashOfConfig": "25"}, {"size": 7209, "mtime": 1749656983481, "results": "33", "hashOfConfig": "25"}, {"size": 1751, "mtime": 1749654522163, "results": "34", "hashOfConfig": "25"}, {"size": 10469, "mtime": 1749655900354, "results": "35", "hashOfConfig": "25"}, {"size": 10117, "mtime": 1749655900795, "results": "36", "hashOfConfig": "25"}, {"size": 6335, "mtime": 1749653704700, "results": "37", "hashOfConfig": "25"}, {"size": 8789, "mtime": 1749653771719, "results": "38", "hashOfConfig": "25"}, {"size": 10592, "mtime": 1749653814527, "results": "39", "hashOfConfig": "25"}, {"size": 10308, "mtime": 1749654242256, "results": "40", "hashOfConfig": "25"}, {"size": 5314, "mtime": 1749653734304, "results": "41", "hashOfConfig": "25"}, {"size": 4255, "mtime": 1749657584536, "results": "42", "hashOfConfig": "25"}, {"size": 1567, "mtime": 1749654303454, "results": "43", "hashOfConfig": "25"}, {"size": 3376, "mtime": 1749655901064, "results": "44", "hashOfConfig": "25"}, {"size": 7580, "mtime": 1749653455870, "results": "45", "hashOfConfig": "25"}, {"size": 10877, "mtime": 1749657347128, "results": "46", "hashOfConfig": "25"}, {"size": 12477, "mtime": 1749657411828, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1n9atck", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\drivers\\page.tsx", ["117", "118"], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\operations\\page.tsx", ["119"], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\page.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\reports\\page.tsx", ["120"], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\scanner\\page.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\error.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\layout.tsx", ["121", "122"], [], "D:\\شركة النرجس\\car management\\src\\app\\loading.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\login\\page.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\not-found.tsx", [], [], "D:\\شركة النرجس\\car management\\src\\app\\page.tsx", ["123"], [], "D:\\شركة النرجس\\car management\\src\\components\\Layout.tsx", ["124"], [], "D:\\شركة النرجس\\car management\\src\\components\\QRScanner.tsx", ["125"], [], "D:\\شركة النرجس\\car management\\src\\lib\\drivers.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\operations.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\pdf-generator.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\qr-generator.ts", [], [], "D:\\شركة النرجس\\car management\\src\\lib\\supabase.ts", [], [], "D:\\شركة النرجس\\car management\\src\\middleware.ts", [], [], "D:\\شركة النرجس\\car management\\src\\types\\database.ts", [], [], "D:\\شركة النرجس\\car management\\src\\types\\supabase.ts", [], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\camps\\page.tsx", ["126"], [], "D:\\شركة النرجس\\car management\\src\\app\\dashboard\\users\\page.tsx", ["127"], [], {"ruleId": "128", "severity": 1, "message": "129", "line": 42, "column": 6, "nodeType": "130", "endLine": 42, "endColumn": 45, "suggestions": "131"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 482, "column": 19, "nodeType": "134", "endLine": 486, "endColumn": 21}, {"ruleId": "128", "severity": 1, "message": "135", "line": 31, "column": 6, "nodeType": "130", "endLine": 31, "endColumn": 15, "suggestions": "136"}, {"ruleId": "128", "severity": 1, "message": "137", "line": 46, "column": 6, "nodeType": "130", "endLine": 46, "endColumn": 15, "suggestions": "138"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 26, "column": 9, "nodeType": "134", "endLine": 29, "endColumn": 11}, {"ruleId": "139", "severity": 1, "message": "140", "line": 30, "column": 9, "nodeType": "134", "endLine": 33, "endColumn": 11}, {"ruleId": "128", "severity": 1, "message": "141", "line": 15, "column": 6, "nodeType": "130", "endLine": 15, "endColumn": 8, "suggestions": "142"}, {"ruleId": "128", "severity": 1, "message": "143", "line": 22, "column": 6, "nodeType": "130", "endLine": 22, "endColumn": 8, "suggestions": "144"}, {"ruleId": "128", "severity": 1, "message": "145", "line": 30, "column": 6, "nodeType": "130", "endLine": 30, "endColumn": 16, "suggestions": "146"}, {"ruleId": "128", "severity": 1, "message": "147", "line": 26, "column": 6, "nodeType": "130", "endLine": 26, "endColumn": 8, "suggestions": "148"}, {"ruleId": "128", "severity": 1, "message": "147", "line": 31, "column": 6, "nodeType": "130", "endLine": 31, "endColumn": 8, "suggestions": "149"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadCamps' and 'loadDrivers'. Either include them or remove the dependency array.", "ArrayExpression", ["150"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'loadOperations'. Either include it or remove the dependency array.", ["151"], "React Hook useEffect has a missing dependency: 'loadReportData'. Either include it or remove the dependency array.", ["152"], "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "React Hook useEffect has a missing dependency: 'checkUser'. Either include it or remove the dependency array.", ["153"], "React Hook useEffect has a missing dependency: 'loadUser'. Either include it or remove the dependency array.", ["154"], "React Hook useEffect has missing dependencies: 'qrScanner', 'startScanner', and 'stopScanner'. Either include them or remove the dependency array.", ["155"], "React Hook useEffect has a missing dependency: 'loadCompanies'. Either include it or remove the dependency array.", ["156"], ["157"], {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, {"desc": "170", "fix": "171"}, {"desc": "170", "fix": "172"}, "Update the dependencies array to be: [currentPage, loadCamps, loadDrivers, searchTerm, selectedCamp]", {"range": "173", "text": "174"}, "Update the dependencies array to be: [filters, loadOperations]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [filters, loadReportData]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [checkUser]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [loadUser]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [isActive, qrScanner, startScanner, stopScanner]", {"range": "183", "text": "184"}, "Update the dependencies array to be: [loadCompanies]", {"range": "185", "text": "186"}, {"range": "187", "text": "186"}, [1325, 1364], "[currentPage, loadCamps, loadDrivers, searchTerm, selectedCamp]", [1048, 1057], "[filters, loadOperations]", [1471, 1480], "[filters, loadReportData]", [389, 391], "[checkUser]", [646, 648], "[loadUser]", [768, 778], "[isActive, qrScanner, startScanner, stopScanner]", [729, 731], "[loadCompanies]", [875, 877]]