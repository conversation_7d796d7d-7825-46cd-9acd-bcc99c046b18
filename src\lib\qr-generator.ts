import QRCode from 'qrcode';
import { Driver } from '@/types/database';

// إعدادات QR Code
const QR_OPTIONS = {
  errorCorrectionLevel: 'M' as const,
  type: 'image/png' as const,
  quality: 0.92,
  margin: 1,
  color: {
    dark: '#000000',
    light: '#FFFFFF'
  },
  width: 256
};

// توليد QR Code للسائق
export const generateDriverQRCode = async (driver: Driver): Promise<string> => {
  try {
    // إنشاء البيانات المشفرة للسائق
    const qrData = {
      type: 'driver',
      id: driver.id,
      code: driver.qr_code,
      name: driver.name,
      vehicle: driver.vehicle_number,
      camp_id: driver.camp_id,
      timestamp: Date.now()
    };

    // تحويل البيانات إلى JSON string
    const dataString = JSON.stringify(qrData);
    
    // توليد QR Code
    const qrCodeDataURL = await QRCode.toDataURL(dataString, QR_OPTIONS);
    
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('فشل في توليد رمز QR');
  }
};

// توليد QR Code بسيط (نص فقط)
export const generateSimpleQRCode = async (text: string): Promise<string> => {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(text, QR_OPTIONS);
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating simple QR code:', error);
    throw new Error('فشل في توليد رمز QR');
  }
};

// فك تشفير بيانات QR Code
export const parseQRCodeData = (qrData: string): any => {
  try {
    // محاولة فك التشفير كـ JSON
    const parsedData = JSON.parse(qrData);
    
    // التحقق من صحة البيانات
    if (parsedData.type === 'driver' && parsedData.id && parsedData.code) {
      return parsedData;
    }
    
    // إذا لم تكن البيانات بالتنسيق المتوقع، إرجاع النص كما هو
    return { type: 'text', data: qrData };
  } catch (error) {
    // إذا فشل فك التشفير، إرجاع النص كما هو
    return { type: 'text', data: qrData };
  }
};

// التحقق من صحة QR Code للسائق
export const validateDriverQRCode = (qrData: any): boolean => {
  return (
    qrData &&
    qrData.type === 'driver' &&
    qrData.id &&
    qrData.code &&
    qrData.name &&
    qrData.camp_id
  );
};

// توليد رمز QR فريد للسائق
export const generateUniqueDriverCode = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `DRV_${timestamp}_${random}`.toUpperCase();
};

// إنشاء URL للوصول المباشر للسائق
export const generateDriverURL = (driverId: string): string => {
  const baseURL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  return `${baseURL}/driver/${driverId}`;
};

// توليد QR Code مع معلومات إضافية
export const generateAdvancedDriverQRCode = async (
  driver: Driver,
  additionalInfo?: {
    operation_type?: 'entry' | 'exit';
    material_id?: string;
    notes?: string;
  }
): Promise<string> => {
  try {
    const qrData = {
      type: 'driver_operation',
      driver: {
        id: driver.id,
        code: driver.qr_code,
        name: driver.name,
        vehicle: driver.vehicle_number,
        camp_id: driver.camp_id
      },
      operation: additionalInfo || {},
      timestamp: Date.now(),
      version: '1.0'
    };

    const dataString = JSON.stringify(qrData);
    const qrCodeDataURL = await QRCode.toDataURL(dataString, {
      ...QR_OPTIONS,
      width: 300 // حجم أكبر للمعلومات الإضافية
    });
    
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating advanced QR code:', error);
    throw new Error('فشل في توليد رمز QR المتقدم');
  }
};

// توليد QR Code للطباعة (دقة عالية)
export const generatePrintableQRCode = async (driver: Driver): Promise<string> => {
  try {
    const qrData = {
      type: 'driver',
      id: driver.id,
      code: driver.qr_code,
      name: driver.name,
      vehicle: driver.vehicle_number,
      camp_id: driver.camp_id,
      timestamp: Date.now()
    };

    const dataString = JSON.stringify(qrData);
    
    // إعدادات للطباعة (دقة عالية)
    const printOptions = {
      ...QR_OPTIONS,
      width: 512, // دقة عالية للطباعة
      margin: 2,
      quality: 1.0
    };
    
    const qrCodeDataURL = await QRCode.toDataURL(dataString, printOptions);
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating printable QR code:', error);
    throw new Error('فشل في توليد رمز QR للطباعة');
  }
};

// تصدير دالة للحصول على معلومات QR Code
export const getQRCodeInfo = (qrData: string) => {
  const parsed = parseQRCodeData(qrData);
  
  if (parsed.type === 'driver') {
    return {
      isValid: true,
      type: 'driver',
      driverId: parsed.id,
      driverCode: parsed.code,
      driverName: parsed.name,
      vehicleNumber: parsed.vehicle,
      campId: parsed.camp_id,
      timestamp: parsed.timestamp
    };
  }
  
  return {
    isValid: false,
    type: 'unknown',
    rawData: qrData
  };
};
