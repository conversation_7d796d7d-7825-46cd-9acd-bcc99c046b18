"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { supabase } from "@/lib/supabase";
import { Camp, Company } from "@/types/database";

export default function CampsPage() {
  const [camps, setCamps] = useState<Camp[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCamp, setEditingCamp] = useState<Camp | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    location: "",
    description: "",
    company_id: "",
  });

  useEffect(() => {
    loadCamps();
    loadCompanies();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const loadCamps = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from("camps")
        .select(
          `
          *,
          company:companies(id, name)
        `
        )
        .order("created_at", { ascending: false });

      if (error) throw error;
      setCamps((data || []) as Camp[]);
    } catch (error) {
      console.error("Error loading camps:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCompanies = async () => {
    try {
      const { data, error } = await supabase
        .from("companies")
        .select("*")
        .order("name");

      if (error) throw error;
      setCompanies(data || []);
      if (data && data.length > 0 && !formData.company_id) {
        setFormData((prev) => ({ ...prev, company_id: data[0].id }));
      }
    } catch (error) {
      console.error("Error loading companies:", error);
    }
  };

  const handleSubmit = async () => {
    try {
      if (!formData.name || !formData.location || !formData.company_id) {
        alert("يرجى ملء جميع الحقول المطلوبة");
        return;
      }

      if (editingCamp) {
        // تحديث كمب موجود
        const { error } = await supabase
          .from("camps")
          .update(formData)
          .eq("id", editingCamp.id);

        if (error) throw error;
      } else {
        // إضافة كمب جديد
        const { error } = await supabase.from("camps").insert(formData);

        if (error) throw error;
      }

      setShowAddModal(false);
      setEditingCamp(null);
      setFormData({
        name: "",
        location: "",
        description: "",
        company_id: companies[0]?.id || "",
      });
      loadCamps();
    } catch (error) {
      console.error("Error saving camp:", error);
      alert("فشل في حفظ الكمب");
    }
  };

  const handleEdit = (camp: Camp) => {
    setEditingCamp(camp);
    setFormData({
      name: camp.name,
      location: camp.location,
      description: camp.description || "",
      company_id: camp.company_id,
    });
    setShowAddModal(true);
  };

  const handleDelete = async (camp: Camp) => {
    if (confirm(`هل أنت متأكد من حذف الكمب: ${camp.name}؟`)) {
      try {
        const { error } = await supabase
          .from("camps")
          .update({ is_active: false })
          .eq("id", camp.id);

        if (error) throw error;
        loadCamps();
      } catch (error) {
        console.error("Error deleting camp:", error);
        alert("فشل في حذف الكمب");
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      location: "",
      description: "",
      company_id: companies[0]?.id || "",
    });
    setEditingCamp(null);
    setShowAddModal(false);
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الكمبات</h1>
            <p className="mt-1 text-sm text-gray-600">
              إضافة وإدارة الكمبات التابعة للشركات
            </p>
          </div>
          <button onClick={() => setShowAddModal(true)} className="btn-primary">
            إضافة كمب جديد
          </button>
        </div>

        {/* Camps Table */}
        <div className="card">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <div className="table-container">
              <table className="table">
                <thead>
                  <tr>
                    <th>اسم الكمب</th>
                    <th>الموقع</th>
                    <th>الشركة</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {camps.map((camp) => (
                    <tr key={camp.id}>
                      <td className="font-medium">{camp.name}</td>
                      <td>{camp.location}</td>
                      <td>{camp.company?.name}</td>
                      <td>
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            camp.is_active
                              ? "bg-success-100 text-success-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {camp.is_active ? "نشط" : "غير نشط"}
                        </span>
                      </td>
                      <td>
                        {new Date(camp.created_at).toLocaleDateString("ar-SA")}
                      </td>
                      <td>
                        <div className="flex space-x-2 space-x-reverse">
                          <button
                            onClick={() => handleEdit(camp)}
                            className="text-blue-600 hover:text-blue-900"
                            title="تعديل"
                          >
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDelete(camp)}
                            className="text-red-600 hover:text-red-900"
                            title="حذف"
                          >
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {camps.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">لا توجد كمبات</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingCamp ? "تعديل الكمب" : "إضافة كمب جديد"}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم الكمب *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    className="input-field"
                    placeholder="اسم الكمب"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الموقع *
                  </label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        location: e.target.value,
                      }))
                    }
                    className="input-field"
                    placeholder="موقع الكمب"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الشركة *
                  </label>
                  <select
                    value={formData.company_id}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        company_id: e.target.value,
                      }))
                    }
                    className="input-field"
                  >
                    <option value="">اختر الشركة</option>
                    {companies.map((company) => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الوصف
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    rows={3}
                    className="input-field"
                    placeholder="وصف الكمب"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 space-x-reverse mt-6">
                <button onClick={resetForm} className="btn-secondary">
                  إلغاء
                </button>
                <button onClick={handleSubmit} className="btn-primary">
                  {editingCamp ? "تحديث" : "إضافة"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}
