# نظام إدارة سيارات الحمل

نظام ويب شامل لإدارة ومتابعة عمليات دخول وخروج سيارات الحمل في الكمبات مع دعم QR Code والتقارير المفصلة.

## المميزات الرئيسية

- ✅ **إدارة متعددة الكمبات**: دعم عدة كمبات تحت شركة واحدة
- ✅ **إدارة السائقين**: إضافة وتحديث بيانات السائقين مع QR Code
- ✅ **تسجيل العمليات**: تسجيل سريع للعمليات باستخدام QR Code
- ✅ **نظام صلاحيات متقدم**: أدوار مختلفة (مدير، محاسب، عامل)
- ✅ **التقارير والإحصائيات**: تقارير مفصلة قابلة للتصدير
- ✅ **واجهة عربية**: دعم كامل للغة العربية والاتجاه RTL

## التقنيات المستخدمة

- **Frontend**: Next.js 14 مع TypeScript
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **UI**: Tailwind CSS
- **QR Code**: qrcode و qr-scanner
- **PDF**: jsPDF

## متطلبات النظام

- Node.js 18+ 
- npm أو yarn
- حساب Supabase

## التثبيت والإعداد

### 1. استنساخ المشروع

\`\`\`bash
git clone <repository-url>
cd car-management
\`\`\`

### 2. تثبيت التبعيات

\`\`\`bash
npm install
\`\`\`

### 3. إعداد قاعدة البيانات (Supabase)

1. إنشاء مشروع جديد في [Supabase](https://supabase.com)
2. نسخ URL المشروع و API Key
3. تشغيل SQL Scripts لإنشاء الجداول (انظر مجلد `database/`)

### 4. إعداد متغيرات البيئة

انسخ ملف `.env.local.example` إلى `.env.local` وأضف القيم المطلوبة:

\`\`\`bash
cp .env.local.example .env.local
\`\`\`

\`\`\`env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
\`\`\`

### 5. تشغيل المشروع

\`\`\`bash
npm run dev
\`\`\`

المشروع سيعمل على: http://localhost:3000

## هيكل المشروع

\`\`\`
src/
├── app/                    # صفحات Next.js
│   ├── dashboard/         # لوحة التحكم
│   ├── login/            # تسجيل الدخول
│   └── page.tsx          # الصفحة الرئيسية
├── components/            # المكونات المشتركة
├── lib/                  # المكتبات والخدمات
├── types/                # أنواع TypeScript
└── styles/               # ملفات CSS
\`\`\`

## الأدوار والصلاحيات

### مدير النظام (Admin)
- إنشاء وإدارة الكمبات
- إضافة المستخدمين
- الوصول لجميع البيانات

### مدير الكمب (Camp Manager)
- إدارة السائقين في الكمب
- مراجعة العمليات
- إضافة مستخدمين للكمب

### المحاسب (Accountant)
- عرض التقارير
- تصدير البيانات
- مراجعة العمليات

### العامل (Operator)
- مسح QR Code
- تسجيل العمليات
- واجهة مبسطة

## استخدام النظام

### 1. إضافة سائق جديد
1. الذهاب إلى صفحة "السائقين"
2. النقر على "إضافة سائق جديد"
3. ملء البيانات المطلوبة
4. سيتم توليد QR Code تلقائياً

### 2. تسجيل عملية
1. الذهاب إلى صفحة "مسح QR"
2. مسح QR Code الخاص بالسائق
3. اختيار نوع العملية والمادة
4. تأكيد العملية

### 3. عرض التقارير
1. الذهاب إلى صفحة "التقارير"
2. اختيار الفلاتر المطلوبة
3. عرض أو تصدير التقرير

## قاعدة البيانات

### الجداول الرئيسية:

- `companies`: الشركات الأم
- `camps`: الكمبات
- `users`: المستخدمين
- `drivers`: السائقين
- `materials`: المواد (رمل، حصو، إلخ)
- `operations`: العمليات

## التطوير

### إضافة ميزة جديدة

1. إنشاء المكونات في `src/components/`
2. إضافة الصفحات في `src/app/`
3. تحديث أنواع البيانات في `src/types/`
4. إضافة الخدمات في `src/lib/`

### اختبار النظام

\`\`\`bash
npm run lint
npm run build
\`\`\`

## النشر

### Vercel (موصى به)

1. ربط المشروع بـ Vercel
2. إضافة متغيرات البيئة
3. النشر التلقائي

### خادم مخصص

\`\`\`bash
npm run build
npm start
\`\`\`

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المشروع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
