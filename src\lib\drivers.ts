import { supabase } from './supabase';
import { Driver, CreateDriverData, UpdateDriverData, PaginatedResponse } from '@/types/database';
import { generateUniqueDriverCode } from './qr-generator';

// الحصول على جميع السائقين مع التصفية والترقيم
export const getDrivers = async (
  campId?: string,
  page: number = 1,
  limit: number = 10,
  search?: string,
  isActive?: boolean
): Promise<PaginatedResponse<Driver>> => {
  try {
    let query = supabase
      .from('drivers')
      .select(`
        *,
        camp:camps(
          id,
          name,
          location,
          company:companies(id, name)
        )
      `, { count: 'exact' });

    // تطبيق الفلاتر
    if (campId) {
      query = query.eq('camp_id', campId);
    }

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,phone.ilike.%${search}%,vehicle_number.ilike.%${search}%`);
    }

    // تطبيق الترقيم
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await query
      .range(from, to)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      total_pages: totalPages
    };
  } catch (error) {
    console.error('Error fetching drivers:', error);
    throw error;
  }
};

// الحصول على سائق واحد
export const getDriver = async (id: string): Promise<Driver | null> => {
  try {
    const { data, error } = await supabase
      .from('drivers')
      .select(`
        *,
        camp:camps(
          id,
          name,
          location,
          company:companies(id, name)
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // السائق غير موجود
      }
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error fetching driver:', error);
    throw error;
  }
};

// الحصول على سائق بواسطة QR Code
export const getDriverByQRCode = async (qrCode: string): Promise<Driver | null> => {
  try {
    const { data, error } = await supabase
      .from('drivers')
      .select(`
        *,
        camp:camps(
          id,
          name,
          location,
          company:companies(id, name)
        )
      `)
      .eq('qr_code', qrCode)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // السائق غير موجود
      }
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error fetching driver by QR code:', error);
    throw error;
  }
};

// إنشاء سائق جديد
export const createDriver = async (driverData: CreateDriverData): Promise<Driver> => {
  try {
    // توليد QR Code فريد
    let qrCode = generateUniqueDriverCode();
    
    // التأكد من أن QR Code فريد
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 5;

    while (!isUnique && attempts < maxAttempts) {
      const { data: existingDriver } = await supabase
        .from('drivers')
        .select('id')
        .eq('qr_code', qrCode)
        .single();

      if (!existingDriver) {
        isUnique = true;
      } else {
        qrCode = generateUniqueDriverCode();
        attempts++;
      }
    }

    if (!isUnique) {
      throw new Error('فشل في توليد رمز QR فريد');
    }

    const { data, error } = await supabase
      .from('drivers')
      .insert({
        ...driverData,
        qr_code: qrCode
      })
      .select(`
        *,
        camp:camps(
          id,
          name,
          location,
          company:companies(id, name)
        )
      `)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error creating driver:', error);
    throw error;
  }
};

// تحديث بيانات السائق
export const updateDriver = async (id: string, updateData: UpdateDriverData): Promise<Driver> => {
  try {
    const { data, error } = await supabase
      .from('drivers')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        camp:camps(
          id,
          name,
          location,
          company:companies(id, name)
        )
      `)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error updating driver:', error);
    throw error;
  }
};

// حذف سائق (إلغاء تفعيل)
export const deleteDriver = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('drivers')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }
  } catch (error) {
    console.error('Error deleting driver:', error);
    throw error;
  }
};

// استعادة سائق (إعادة تفعيل)
export const restoreDriver = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('drivers')
      .update({ is_active: true })
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }
  } catch (error) {
    console.error('Error restoring driver:', error);
    throw error;
  }
};

// الحصول على إحصائيات السائق
export const getDriverStats = async (driverId: string) => {
  try {
    const { data, error } = await supabase
      .from('operations')
      .select('operation_type, total_amount, operation_date')
      .eq('driver_id', driverId);

    if (error) {
      throw new Error(error.message);
    }

    const stats = {
      total_operations: data.length,
      total_amount: data.reduce((sum, op) => sum + op.total_amount, 0),
      entry_operations: data.filter(op => op.operation_type === 'entry').length,
      exit_operations: data.filter(op => op.operation_type === 'exit').length,
      last_operation_date: data.length > 0 ? 
        Math.max(...data.map(op => new Date(op.operation_date).getTime())) : null,
      operations_this_month: data.filter(op => {
        const opDate = new Date(op.operation_date);
        const now = new Date();
        return opDate.getMonth() === now.getMonth() && opDate.getFullYear() === now.getFullYear();
      }).length
    };

    return stats;
  } catch (error) {
    console.error('Error fetching driver stats:', error);
    throw error;
  }
};

// البحث عن السائقين
export const searchDrivers = async (
  searchTerm: string,
  campId?: string,
  limit: number = 10
): Promise<Driver[]> => {
  try {
    let query = supabase
      .from('drivers')
      .select(`
        *,
        camp:camps(
          id,
          name,
          location,
          company:companies(id, name)
        )
      `)
      .eq('is_active', true);

    if (campId) {
      query = query.eq('camp_id', campId);
    }

    const { data, error } = await query
      .or(`name.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%,vehicle_number.ilike.%${searchTerm}%`)
      .limit(limit)
      .order('name');

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error('Error searching drivers:', error);
    throw error;
  }
};

// الحصول على السائقين النشطين في كمب معين
export const getActiveDriversByCamp = async (campId: string): Promise<Driver[]> => {
  try {
    const { data, error } = await supabase
      .from('drivers')
      .select(`
        *,
        camp:camps(
          id,
          name,
          location,
          company:companies(id, name)
        )
      `)
      .eq('camp_id', campId)
      .eq('is_active', true)
      .order('name');

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching active drivers by camp:', error);
    throw error;
  }
};

// تحديث QR Code للسائق
export const regenerateDriverQRCode = async (driverId: string): Promise<string> => {
  try {
    const newQRCode = generateUniqueDriverCode();
    
    const { error } = await supabase
      .from('drivers')
      .update({ qr_code: newQRCode })
      .eq('id', driverId);

    if (error) {
      throw new Error(error.message);
    }

    return newQRCode;
  } catch (error) {
    console.error('Error regenerating QR code:', error);
    throw error;
  }
};
