"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { signIn, getUserProfile } from "@/lib/supabase";
import Link from "next/link";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    console.log("🔄 محاولة تسجيل الدخول...", { email });

    try {
      const result = await signIn(email, password);
      console.log("✅ تم تسجيل الدخول بنجاح:", result);

      // التحقق من وجود المستخدم في جدول users
      const userProfile = await getUserProfile();
      console.log("👤 ملف المستخدم:", userProfile);

      if (!userProfile) {
        setError("المستخدم غير موجود في النظام. يرجى التواصل مع المدير.");
        return;
      }

      console.log("🔄 إعادة توجيه إلى لوحة التحكم...");

      // إعادة تحميل الصفحة للتأكد من تحديث الجلسة
      window.location.href = "/dashboard";
    } catch (error: any) {
      console.error("❌ خطأ في تسجيل الدخول:", error);
      setError(error.message || "حدث خطأ في تسجيل الدخول");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
            <svg
              className="h-6 w-6 text-primary-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            تسجيل الدخول
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            أو{" "}
            <Link
              href="/"
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              العودة للصفحة الرئيسية
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit} noValidate>
          <div className="rounded-md shadow-sm space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                البريد الإلكتروني
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="input-field"
                placeholder="أدخل البريد الإلكتروني"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                كلمة المرور
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="input-field"
                placeholder="أدخل كلمة المرور"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Debug Info */}
          <div className="bg-gray-50 border border-gray-200 text-gray-700 px-4 py-3 rounded-lg text-sm">
            <p>
              <strong>حالة التحميل:</strong>{" "}
              {isLoading ? "جاري التحميل..." : "جاهز"}
            </p>
            <p>
              <strong>البريد المدخل:</strong> {email || "لم يتم إدخال بريد"}
            </p>
            <p>
              <strong>كلمة المرور:</strong>{" "}
              {password ? "***" : "لم يتم إدخال كلمة مرور"}
            </p>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading || !email || !password}
              onClick={(e) => {
                console.log("🔘 تم النقر على زر تسجيل الدخول");
                console.log("📧 البريد:", email);
                console.log("🔒 كلمة المرور موجودة:", !!password);
              }}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري تسجيل الدخول...
                </div>
              ) : (
                "تسجيل الدخول"
              )}
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              نسيت كلمة المرور؟{" "}
              <a
                href="#"
                className="font-medium text-primary-600 hover:text-primary-500"
              >
                إعادة تعيين
              </a>
            </p>
          </div>
        </form>

        {/* Demo Accounts */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            حسابات تجريبية:
          </h3>
          <div className="text-xs text-blue-700 space-y-1">
            <p>
              <strong>مدير النظام:</strong> <EMAIL> / password123
            </p>
            <p>
              <strong>مدير كمب:</strong> <EMAIL> / password123
            </p>
            <p>
              <strong>محاسب:</strong> <EMAIL> / password123
            </p>
            <p>
              <strong>عامل:</strong> <EMAIL> / password123
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
