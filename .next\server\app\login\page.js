/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\")), \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1QyVEOCVCNCVEOCVCMSVEOSU4MyVEOCVBOSUyMCVEOCVBNyVEOSU4NCVEOSU4NiVEOCVCMSVEOCVBQyVEOCVCMyU1Q2NhciUyMG1hbmFnZW1lbnQlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2xpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FyLW1hbmFnZW1lbnQtc3lzdGVtLz9jZjQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxc2LTYsdmD2Kkg2KfZhNmG2LHYrNizXFxcXGNhciBtYW5hZ2VtZW50XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Cstyles%5Cglobals.css&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Cstyles%5Cglobals.css&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp%5Cerror.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp%5Cerror.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1QyVEOCVCNCVEOCVCMSVEOSU4MyVEOCVBOSUyMCVEOCVBNyVEOSU4NCVEOSU4NiVEOCVCMSVEOCVBQyVEOCVCMyU1Q2NhciUyMG1hbmFnZW1lbnQlNUNzcmMlNUNhcHAlNUNlcnJvci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FyLW1hbmFnZW1lbnQtc3lzdGVtLz80MWViIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxc2LTYsdmD2Kkg2KfZhNmG2LHYrNizXFxcXGNhciBtYW5hZ2VtZW50XFxcXHNyY1xcXFxhcHBcXFxcZXJyb3IudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp%5Cerror.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1QyVEOCVCNCVEOCVCMSVEOSU4MyVEOCVBOSUyMCVEOCVBNyVEOSU4NCVEOSU4NiVEOCVCMSVEOCVBQyVEOCVCMyU1Q2NhciUyMG1hbmFnZW1lbnQlNUNzcmMlNUNhcHAlNUNsb2dpbiU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Nhci1tYW5hZ2VtZW50LXN5c3RlbS8/NTNlMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXNi02LHZg9ipINin2YTZhtix2KzYs1xcXFxjYXIgbWFuYWdlbWVudFxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.error(error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white shadow-lg rounded-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"حدث خطأ غير متوقع\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex flex-col space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 transition-colors\",\n                            children: \"المحاولة مرة أخرى\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\",\n                            children: \"العودة للصفحة الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-sm text-gray-500 cursor-pointer\",\n                            children: \"تفاصيل الخطأ (للمطورين)\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction LoginPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        console.log(\"\\uD83D\\uDD04 محاولة تسجيل الدخول...\", {\n            email\n        });\n        try {\n            const result = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.signIn)(email, password);\n            console.log(\"✅ تم تسجيل الدخول بنجاح:\", result);\n            // التحقق من وجود المستخدم في جدول users\n            const userProfile = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getUserProfile)();\n            console.log(\"\\uD83D\\uDC64 ملف المستخدم:\", userProfile);\n            if (!userProfile) {\n                setError(\"المستخدم غير موجود في النظام. يرجى التواصل مع المدير.\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDD04 إعادة توجيه إلى لوحة التحكم...\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"❌ خطأ في تسجيل الدخول:\", error);\n            setError(error.message || \"حدث خطأ في تسجيل الدخول\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6 text-primary-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-bold text-gray-900\",\n                            children: \"تسجيل الدخول\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: [\n                                \"أو\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/\",\n                                    className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                    children: \"العودة للصفحة الرئيسية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    noValidate: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md shadow-sm space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"البريد الإلكتروني\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"input-field\",\n                                            placeholder: \"أدخل البريد الإلكتروني\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            required: true,\n                                            className: \"input-field\",\n                                            placeholder: \"أدخل كلمة المرور\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 border border-gray-200 text-gray-700 px-4 py-3 rounded-lg text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"حالة التحميل:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        isLoading ? \"جاري التحميل...\" : \"جاهز\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"البريد المدخل:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        email || \"لم يتم إدخال بريد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"كلمة المرور:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        password ? \"***\" : \"لم يتم إدخال كلمة مرور\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading || !email || !password,\n                                onClick: (e)=>{\n                                    console.log(\"\\uD83D\\uDD18 تم النقر على زر تسجيل الدخول\");\n                                    console.log(\"\\uD83D\\uDCE7 البريد:\", email);\n                                    console.log(\"\\uD83D\\uDD12 كلمة المرور موجودة:\", !!password);\n                                },\n                                className: \"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"جاري تسجيل الدخول...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this) : \"تسجيل الدخول\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"نسيت كلمة المرور؟\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                        children: \"إعادة تعيين\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 p-4 bg-blue-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                            children: \"حسابات تجريبية:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-700 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"مدير النظام:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" <EMAIL> / password123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"مدير كمب:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" <EMAIL> / password123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"محاسب:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" <EMAIL> / password123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"عامل:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" <EMAIL> / password123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkUserPermissions: () => (/* binding */ checkUserPermissions),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getCampsByCompany: () => (/* binding */ getCampsByCompany),\n/* harmony export */   getCompanies: () => (/* binding */ getCompanies),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://iueprblbgbqahhzwzmzp.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml1ZXByYmxiZ2JxYWhoend6bXpwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQzNTUsImV4cCI6MjA2NTIzMDM1NX0.579OWXE1sGDmqbzsE8GXe4vv2QR1YGa2jh4m0EVYv5E\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// دالة للحصول على المستخدم الحالي\nconst getCurrentUser = async ()=>{\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) {\n        console.error(\"Error getting current user:\", error);\n        return null;\n    }\n    return user;\n};\n// دالة للتحقق من صلاحيات المستخدم\nconst checkUserPermissions = async (requiredRole)=>{\n    const user = await getCurrentUser();\n    if (!user) return false;\n    const { data: userProfile, error } = await supabase.from(\"users\").select(\"role, camp_id, company_id\").eq(\"id\", user.id).single();\n    if (error || !userProfile) return false;\n    // منطق التحقق من الصلاحيات\n    switch(requiredRole){\n        case \"admin\":\n            return userProfile.role === \"admin\";\n        case \"camp_manager\":\n            return [\n                \"admin\",\n                \"camp_manager\"\n            ].includes(userProfile.role);\n        case \"accountant\":\n            return [\n                \"admin\",\n                \"camp_manager\",\n                \"accountant\"\n            ].includes(userProfile.role);\n        case \"operator\":\n            return [\n                \"admin\",\n                \"camp_manager\",\n                \"accountant\",\n                \"operator\"\n            ].includes(userProfile.role);\n        default:\n            return false;\n    }\n};\n// دالة للحصول على معلومات المستخدم الكاملة\nconst getUserProfile = async ()=>{\n    const user = await getCurrentUser();\n    if (!user) return null;\n    const { data: userProfile, error } = await supabase.from(\"users\").select(`\n      *,\n      camp:camps(*),\n      company:companies(*)\n    `).eq(\"id\", user.id).single();\n    if (error) {\n        console.error(\"Error getting user profile:\", error);\n        return null;\n    }\n    return userProfile;\n};\n// دالة لتسجيل الدخول\nconst signIn = async (email, password)=>{\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n// دالة لتسجيل الخروج\nconst signOut = async ()=>{\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        throw new Error(error.message);\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    // إنشاء المستخدم في نظام المصادقة\n    const { data: authData, error: authError } = await supabase.auth.signUp({\n        email: userData.email,\n        password: userData.password\n    });\n    if (authError) {\n        throw new Error(authError.message);\n    }\n    if (!authData.user) {\n        throw new Error(\"فشل في إنشاء المستخدم\");\n    }\n    // إضافة بيانات المستخدم إلى جدول المستخدمين\n    const { data: userProfile, error: profileError } = await supabase.from(\"users\").insert({\n        id: authData.user.id,\n        email: userData.email,\n        full_name: userData.full_name,\n        phone: userData.phone,\n        role: userData.role,\n        camp_id: userData.camp_id,\n        company_id: userData.company_id\n    }).select().single();\n    if (profileError) {\n        throw new Error(profileError.message);\n    }\n    return {\n        authData,\n        userProfile\n    };\n};\n// دالة للحصول على الكمبات حسب الشركة\nconst getCampsByCompany = async (companyId)=>{\n    const { data, error } = await supabase.from(\"camps\").select(\"*\").eq(\"company_id\", companyId).eq(\"is_active\", true).order(\"name\");\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n// دالة للحصول على جميع الشركات\nconst getCompanies = async ()=>{\n    const { data, error } = await supabase.from(\"companies\").select(\"*\").order(\"name\");\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7c76db1315e7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FyLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3N0eWxlcy9nbG9iYWxzLmNzcz81NzkzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2M3NmRiMTMxNWU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\شركة النرجس\car management\src\app\error.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"نظام إدارة سيارات الحمل\",\n    description: \"نظام إدارة عمليات دخول وخروج سيارات الحمل في الكمبات\",\n    keywords: \"إدارة سيارات، كمبات، سائقين، عمليات نقل\",\n    authors: [\n        {\n            name: \"فريق التطوير\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"noindex, nofollow\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} font-arabic`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600\",\n                    children: \"جاري التحميل...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJMUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXItbWFuYWdlbWVudC1zeXN0ZW0vLi9zcmMvYXBwL2xvYWRpbmcudHN4PzljZDkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmF5LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItcHJpbWFyeS02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtZ3JheS02MDBcIj7YrNin2LHZiiDYp9mE2KrYrdmF2YrZhC4uLjwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\شركة النرجس\car management\src\app\login\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-9xl font-bold text-primary-600\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900 mt-4\",\n                            children: \"الصفحة غير موجودة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"عذراً، لا يمكن العثور على الصفحة التي تبحث عنها.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"inline-block bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors\",\n                            children: \"العودة للصفحة الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/dashboard\",\n                                className: \"text-primary-600 hover:text-primary-800 text-sm\",\n                                children: \"أو اذهب إلى لوحة التحكم\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-64 h-64 mx-auto text-gray-300\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 0.5,\n                            d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29.82-5.657 2.172M12 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H5z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%D8%B4%D8%B1%D9%83%D8%A9%20%D8%A7%D9%84%D9%86%D8%B1%D8%AC%D8%B3%5Ccar%20management&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();