-- بيانات تجريبية لنظام إدارة سيارات الحمل
-- تشغيل هذا الملف بعد إنشاء الجداول

-- إدراج شركة تجريبية
INSERT INTO companies (id, name, description) VALUES 
('550e8400-e29b-41d4-a716-************', 'شركة النرجس للمقاولات', 'شركة متخصصة في أعمال المقاولات والبناء');

-- إدراج كمبات تجريبية
INSERT INTO camps (id, company_id, name, location, description) VALUES 
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'كمب بغداد الرئيسي', 'منطقة الدورة - بغداد', 'الكمب الرئيسي في بغداد'),
('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-************', 'كمب البصرة', 'منطقة الزبير - البصرة', 'كمب فرعي في البصرة'),
('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', 'كمب أربيل', 'منطقة عنكاوا - أربيل', 'كمب فرعي في أربيل');

-- إدراج مواد تجريبية
INSERT INTO materials (id, name, description, unit_price, unit) VALUES 
('550e8400-e29b-41d4-a716-446655440010', 'رمل', 'رمل بناء عادي', 11000.00, 'طن'),
('550e8400-e29b-41d4-a716-446655440011', 'حصو', 'حصو بناء مختلط', 15000.00, 'طن'),
('550e8400-e29b-41d4-a716-446655440012', 'تراب', 'تراب ردم', 8000.00, 'طن'),
('550e8400-e29b-41d4-a716-446655440013', 'إسمنت', 'إسمنت عادي', 12000.00, 'كيس'),
('550e8400-e29b-41d4-a716-446655440014', 'بلوك', 'بلوك بناء', 500.00, 'قطعة');

-- ملاحظة: المستخدمين سيتم إنشاؤهم عبر نظام المصادقة
-- هذه أمثلة على البيانات التي ستكون موجودة بعد التسجيل

-- إدراج سائقين تجريبيين (سيتم إنشاؤهم بعد إنشاء المستخدمين)
-- INSERT INTO drivers (id, camp_id, name, phone, vehicle_type, vehicle_number, qr_code) VALUES 
-- ('550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-************', 'أحمد محمد علي', '07901234567', 'شاحنة كبيرة', 'بغداد 12345', 'DRV_AHMED001'),
-- ('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-************', 'محمد حسن جعفر', '07902345678', 'شاحنة متوسطة', 'بغداد 23456', 'DRV_MOHAMMED002'),
-- ('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440002', 'علي حسين كاظم', '07903456789', 'شاحنة كبيرة', 'البصرة 34567', 'DRV_ALI003');

-- دالة لإنشاء مستخدم تجريبي (يجب تشغيلها بعد إنشاء المستخدمين في نظام المصادقة)
CREATE OR REPLACE FUNCTION create_demo_users()
RETURNS void AS $$
BEGIN
    -- هذه الدالة ستحتاج لتشغيلها يدوياً بعد إنشاء المستخدمين في Supabase Auth
    -- أو يمكن استخدام Supabase Dashboard لإنشاء المستخدمين
    
    -- مثال على إدراج بيانات المستخدمين (بعد إنشائهم في Auth)
    /*
    INSERT INTO users (id, email, full_name, phone, role, camp_id, company_id) VALUES 
    ('user-id-from-auth', '<EMAIL>', 'مدير النظام', '***********', 'admin', NULL, '550e8400-e29b-41d4-a716-************'),
    ('user-id-from-auth-2', '<EMAIL>', 'مدير كمب بغداد', '***********', 'camp_manager', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
    ('user-id-from-auth-3', '<EMAIL>', 'محاسب', '***********', 'accountant', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'),
    ('user-id-from-auth-4', '<EMAIL>', 'عامل', '***********', 'operator', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************');
    */
    
    RAISE NOTICE 'يجب إنشاء المستخدمين في Supabase Auth أولاً ثم إضافة بياناتهم هنا';
END;
$$ language 'plpgsql';

-- دالة لإنشاء بيانات تجريبية كاملة
CREATE OR REPLACE FUNCTION create_sample_operations()
RETURNS void AS $$
DECLARE
    driver_id UUID;
    material_id UUID;
    user_id UUID;
    camp_id UUID;
BEGIN
    -- الحصول على معرفات للاستخدام في العمليات التجريبية
    SELECT id INTO camp_id FROM camps LIMIT 1;
    SELECT id INTO material_id FROM materials WHERE name = 'رمل' LIMIT 1;
    
    -- إنشاء عمليات تجريبية (بعد إنشاء السائقين والمستخدمين)
    /*
    INSERT INTO operations (camp_id, driver_id, material_id, operation_type, quantity, unit_price, operator_id, operation_date) VALUES 
    (camp_id, driver_id, material_id, 'entry', 2.5, 11000.00, user_id, NOW() - INTERVAL '1 day'),
    (camp_id, driver_id, material_id, 'exit', 1.0, 11000.00, user_id, NOW() - INTERVAL '2 hours');
    */
    
    RAISE NOTICE 'يجب إنشاء السائقين والمستخدمين أولاً';
END;
$$ language 'plpgsql';

-- إنشاء view للإحصائيات
CREATE OR REPLACE VIEW dashboard_stats AS
SELECT 
    COUNT(CASE WHEN DATE(operation_date) = CURRENT_DATE THEN 1 END) as operations_today,
    COALESCE(SUM(CASE WHEN DATE(operation_date) = CURRENT_DATE THEN total_amount END), 0) as revenue_today,
    COUNT(CASE WHEN DATE(operation_date) >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as operations_month,
    COALESCE(SUM(CASE WHEN DATE(operation_date) >= DATE_TRUNC('month', CURRENT_DATE) THEN total_amount END), 0) as revenue_month
FROM operations;

-- إنشاء view لتقرير العمليات المفصل
CREATE OR REPLACE VIEW operations_report AS
SELECT 
    o.id,
    o.operation_date,
    o.operation_type,
    d.name as driver_name,
    d.vehicle_number,
    m.name as material_name,
    o.quantity,
    o.unit_price,
    o.total_amount,
    c.name as camp_name,
    u.full_name as operator_name,
    o.notes
FROM operations o
JOIN drivers d ON o.driver_id = d.id
JOIN materials m ON o.material_id = m.id
JOIN camps c ON o.camp_id = c.id
JOIN users u ON o.operator_id = u.id
ORDER BY o.operation_date DESC;

-- إنشاء view للسائقين مع إحصائياتهم
CREATE OR REPLACE VIEW drivers_with_stats AS
SELECT 
    d.*,
    c.name as camp_name,
    COUNT(o.id) as total_operations,
    COALESCE(SUM(o.total_amount), 0) as total_revenue,
    MAX(o.operation_date) as last_operation_date
FROM drivers d
LEFT JOIN camps c ON d.camp_id = c.id
LEFT JOIN operations o ON d.id = o.driver_id
GROUP BY d.id, c.name
ORDER BY d.created_at DESC;

-- تعليق: 
-- بعد إنشاء المستخدمين في Supabase Auth، يجب تشغيل الاستعلامات التالية:
-- 1. إضافة بيانات المستخدمين في جدول users
-- 2. إنشاء السائقين التجريبيين
-- 3. إنشاء العمليات التجريبية
