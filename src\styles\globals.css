@tailwind base;
@tailwind components;
@tailwind utilities;

/* استيراد خطوط عربية من Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap");

/* إعدادات عامة للغة العربية */
html {
  direction: rtl;
  font-family: "Tajawal", "Cairo", sans-serif;
}

body {
  direction: rtl;
  text-align: right;
  font-family: "Tajawal", "Cairo", sans-serif;
}

/* تخصيص الألوان والأنماط */
@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* أزرار مخصصة */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  /* حقول الإدخال */
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  /* بطاقات */
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }

  /* جداول */
  .table-container {
    @apply overflow-x-auto shadow-md rounded-lg;
  }

  .table {
    @apply min-w-full bg-white;
  }

  .table th {
    @apply px-6 py-3 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
  }

  /* شريط جانبي */
  .sidebar {
    @apply fixed right-0 top-0 h-full w-64 bg-white shadow-lg border-l border-gray-200 z-50;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors duration-200;
  }

  .sidebar-item.active {
    @apply bg-blue-50 text-blue-700 border-l-4 border-blue-600;
  }
}

/* تحسينات للطباعة */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .sidebar {
    @apply w-full;
  }

  .table-container {
    @apply text-sm;
  }

  .card {
    @apply p-4;
  }
}
