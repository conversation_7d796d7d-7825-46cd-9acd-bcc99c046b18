export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      camps: {
        Row: {
          id: string
          company_id: string
          name: string
          location: string
          description: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          company_id: string
          name: string
          location: string
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          company_id?: string
          name?: string
          location?: string
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "camps_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          phone: string | null
          role: string
          camp_id: string | null
          company_id: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          phone?: string | null
          role: string
          camp_id?: string | null
          company_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          phone?: string | null
          role?: string
          camp_id?: string | null
          company_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_camp_id_fkey"
            columns: ["camp_id"]
            referencedRelation: "camps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "users_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      drivers: {
        Row: {
          id: string
          camp_id: string
          name: string
          phone: string
          vehicle_type: string
          vehicle_number: string
          national_id: string | null
          notes: string | null
          qr_code: string
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          camp_id: string
          name: string
          phone: string
          vehicle_type: string
          vehicle_number: string
          national_id?: string | null
          notes?: string | null
          qr_code: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          camp_id?: string
          name?: string
          phone?: string
          vehicle_type?: string
          vehicle_number?: string
          national_id?: string | null
          notes?: string | null
          qr_code?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "drivers_camp_id_fkey"
            columns: ["camp_id"]
            referencedRelation: "camps"
            referencedColumns: ["id"]
          }
        ]
      }
      materials: {
        Row: {
          id: string
          name: string
          description: string | null
          unit_price: number
          unit: string
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          unit_price: number
          unit: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          unit_price?: number
          unit?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      operations: {
        Row: {
          id: string
          camp_id: string
          driver_id: string
          material_id: string
          operation_type: string
          quantity: number
          unit_price: number
          total_amount: number
          notes: string | null
          operator_id: string
          operation_date: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          camp_id: string
          driver_id: string
          material_id: string
          operation_type: string
          quantity: number
          unit_price: number
          total_amount: number
          notes?: string | null
          operator_id: string
          operation_date: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          camp_id?: string
          driver_id?: string
          material_id?: string
          operation_type?: string
          quantity?: number
          unit_price?: number
          total_amount?: number
          notes?: string | null
          operator_id?: string
          operation_date?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "operations_camp_id_fkey"
            columns: ["camp_id"]
            referencedRelation: "camps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "operations_driver_id_fkey"
            columns: ["driver_id"]
            referencedRelation: "drivers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "operations_material_id_fkey"
            columns: ["material_id"]
            referencedRelation: "materials"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "operations_operator_id_fkey"
            columns: ["operator_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
