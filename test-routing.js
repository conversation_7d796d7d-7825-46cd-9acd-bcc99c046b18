// اختبار التوجيه والمصادقة
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testRouting() {
  console.log('🔍 اختبار التوجيه والمصادقة...\n');

  try {
    // 1. اختبار تسجيل الدخول
    console.log('🔄 1. اختبار تسجيل الدخول...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123',
    });

    if (loginError) {
      console.error('❌ فشل تسجيل الدخول:', loginError.message);
      return false;
    }

    console.log('✅ تم تسجيل الدخول بنجاح');
    console.log('👤 معرف المستخدم:', loginData.user.id);

    // 2. اختبار الحصول على بيانات المستخدم
    console.log('\n🔄 2. اختبار الحصول على بيانات المستخدم...');
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select(`
        *,
        camp:camps(*),
        company:companies(*)
      `)
      .eq('id', loginData.user.id)
      .single();

    if (profileError) {
      console.error('❌ فشل في الحصول على بيانات المستخدم:', profileError.message);
      return false;
    }

    console.log('✅ تم الحصول على بيانات المستخدم');
    console.log('📋 الاسم:', userProfile.full_name);
    console.log('🔑 الدور:', userProfile.role);
    console.log('🏢 الشركة:', userProfile.company?.name || 'غير محدد');
    console.log('🏕️ الكمب:', userProfile.camp?.name || 'غير محدد');

    // 3. اختبار الصلاحيات
    console.log('\n🔄 3. اختبار الصلاحيات...');
    const rolePermissions = {
      admin: ['dashboard', 'drivers', 'operations', 'reports', 'camps', 'users', 'scanner'],
      camp_manager: ['dashboard', 'drivers', 'operations', 'reports', 'users', 'scanner'],
      accountant: ['dashboard', 'drivers', 'operations', 'reports', 'scanner'],
      operator: ['dashboard', 'operations', 'scanner']
    };

    const userPermissions = rolePermissions[userProfile.role] || [];
    console.log('✅ صلاحيات المستخدم:', userPermissions.join(', '));

    // 4. تسجيل الخروج
    console.log('\n🔄 4. اختبار تسجيل الخروج...');
    const { error: signOutError } = await supabase.auth.signOut();

    if (signOutError) {
      console.error('❌ فشل تسجيل الخروج:', signOutError.message);
      return false;
    }

    console.log('✅ تم تسجيل الخروج بنجاح');

    // 5. التحقق من انتهاء الجلسة
    console.log('\n🔄 5. التحقق من انتهاء الجلسة...');
    const { data: sessionData } = await supabase.auth.getSession();

    if (sessionData.session) {
      console.log('⚠️ الجلسة ما زالت نشطة (قد يكون هذا طبيعي)');
    } else {
      console.log('✅ تم إنهاء الجلسة بنجاح');
    }

    return true;

  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
    return false;
  }
}

// تشغيل الاختبار
testRouting().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 اختبار التوجيه والمصادقة نجح!');
    console.log('\n📋 الخطوات التالية:');
    console.log('1. افتح المتصفح: http://localhost:3000');
    console.log('2. يجب أن ترى الصفحة الرئيسية (بدون إعادة توجيه)');
    console.log('3. انقر على "تسجيل الدخول"');
    console.log('4. استخدم: <EMAIL> / password123');
    console.log('5. يجب أن يتم توجيهك إلى /dashboard');
    console.log('\n🔧 إذا لم يعمل:');
    console.log('• تحقق من وحدة التحكم في المتصفح (F12)');
    console.log('• تأكد من تشغيل الخادم على localhost:3000');
    console.log('• جرب مسح cache المتصفح');
  } else {
    console.log('❌ فشل اختبار التوجيه والمصادقة');
    console.log('\n🔧 تحقق من:');
    console.log('• اتصال قاعدة البيانات');
    console.log('• بيانات المستخدم في جدول users');
    console.log('• إعدادات Supabase Auth');
  }
  console.log('='.repeat(50));
  
  process.exit(success ? 0 : 1);
});
