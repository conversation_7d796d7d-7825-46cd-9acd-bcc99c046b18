"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkUser();\n    }, []);\n    const checkUser = async ()=>{\n        try {\n            const currentUser = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n            if (currentUser) {\n                setUser(currentUser);\n                router.push(\"/dashboard\");\n            } else {\n                setIsLoading(false);\n            }\n        } catch (error) {\n            console.error(\"Error checking user:\", error);\n            setIsLoading(false);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-primary-900\",\n                                        children: \"نظام إدارة سيارات الحمل\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/login\",\n                                    className: \"btn-primary\",\n                                    children: \"تسجيل الدخول\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl\",\n                                children: [\n                                    \"مرحباً بك في نظام إدارة\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-600\",\n                                        children: \" سيارات الحمل\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 max-w-2xl mx-auto text-xl text-gray-600\",\n                                children: \"نظام شامل لإدارة ومتابعة عمليات دخول وخروج سيارات الحمل في الكمبات مع دعم QR Code والتقارير المفصلة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex justify-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/login\",\n                                        className: \"btn-primary text-lg px-8 py-3\",\n                                        children: \"ابدأ الآن\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"#features\",\n                                        className: \"btn-secondary text-lg px-8 py-3\",\n                                        children: \"تعرف على المزيد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"features\",\n                        className: \"mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"مميزات النظام\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-4 text-lg text-gray-600\",\n                                        children: \"حلول متكاملة لإدارة عمليات النقل والسائقين\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto bg-primary-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-primary-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mt-4 text-xl font-semibold text-gray-900\",\n                                                children: \"إدارة السائقين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2 text-gray-600\",\n                                                children: \"إضافة وإدارة بيانات السائقين مع توليد بطاقات QR Code رقمية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto bg-success-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-success-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mt-4 text-xl font-semibold text-gray-900\",\n                                                children: \"تسجيل العمليات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2 text-gray-600\",\n                                                children: \"تسجيل سريع لعمليات الدخول والخروج باستخدام QR Code\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto bg-warning-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-warning-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mt-4 text-xl font-semibold text-gray-900\",\n                                                children: \"التقارير والإحصائيات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2 text-gray-600\",\n                                                children: \"تقارير مفصلة وإحصائيات شاملة لجميع العمليات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto bg-purple-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-purple-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mt-4 text-xl font-semibold text-gray-900\",\n                                                children: \"دعم متعدد الكمبات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2 text-gray-600\",\n                                                children: \"إدارة عدة كمبات تحت شركة واحدة مع صلاحيات منفصلة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto bg-indigo-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-indigo-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mt-4 text-xl font-semibold text-gray-900\",\n                                                children: \"نظام صلاحيات متقدم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2 text-gray-600\",\n                                                children: \"أدوار مختلفة للمستخدمين مع صلاحيات محددة لكل دور\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto bg-green-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-green-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mt-4 text-xl font-semibold text-gray-900\",\n                                                children: \"حساب التكاليف التلقائي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2 text-gray-600\",\n                                                children: \"حساب تلقائي للتكاليف والمستحقات مع إمكانية التصدير\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 نظام إدارة سيارات الحمل. جميع الحقوق محفوظة.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\شركة النرجس\\\\car management\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"5fepgTz7W0vYDF5VbUMZgQEUunE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkUserPermissions: function() { return /* binding */ checkUserPermissions; },\n/* harmony export */   createUser: function() { return /* binding */ createUser; },\n/* harmony export */   getCampsByCompany: function() { return /* binding */ getCampsByCompany; },\n/* harmony export */   getCompanies: function() { return /* binding */ getCompanies; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getUserProfile: function() { return /* binding */ getUserProfile; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://iueprblbgbqahhzwzmzp.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml1ZXByYmxiZ2JxYWhoend6bXpwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQzNTUsImV4cCI6MjA2NTIzMDM1NX0.579OWXE1sGDmqbzsE8GXe4vv2QR1YGa2jh4m0EVYv5E\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// دالة للحصول على المستخدم الحالي\nconst getCurrentUser = async ()=>{\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) {\n        console.error(\"Error getting current user:\", error);\n        return null;\n    }\n    return user;\n};\n// دالة للتحقق من صلاحيات المستخدم\nconst checkUserPermissions = async (requiredRole)=>{\n    const user = await getCurrentUser();\n    if (!user) return false;\n    const { data: userProfile, error } = await supabase.from(\"users\").select(\"role, camp_id, company_id\").eq(\"id\", user.id).single();\n    if (error || !userProfile) return false;\n    // منطق التحقق من الصلاحيات\n    switch(requiredRole){\n        case \"admin\":\n            return userProfile.role === \"admin\";\n        case \"camp_manager\":\n            return [\n                \"admin\",\n                \"camp_manager\"\n            ].includes(userProfile.role);\n        case \"accountant\":\n            return [\n                \"admin\",\n                \"camp_manager\",\n                \"accountant\"\n            ].includes(userProfile.role);\n        case \"operator\":\n            return [\n                \"admin\",\n                \"camp_manager\",\n                \"accountant\",\n                \"operator\"\n            ].includes(userProfile.role);\n        default:\n            return false;\n    }\n};\n// دالة للحصول على معلومات المستخدم الكاملة\nconst getUserProfile = async ()=>{\n    const user = await getCurrentUser();\n    if (!user) return null;\n    const { data: userProfile, error } = await supabase.from(\"users\").select(\"\\n      *,\\n      camp:camps(*),\\n      company:companies(*)\\n    \").eq(\"id\", user.id).single();\n    if (error) {\n        console.error(\"Error getting user profile:\", error);\n        return null;\n    }\n    return userProfile;\n};\n// دالة لتسجيل الدخول\nconst signIn = async (email, password)=>{\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n// دالة لتسجيل الخروج\nconst signOut = async ()=>{\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        throw new Error(error.message);\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    // إنشاء المستخدم في نظام المصادقة\n    const { data: authData, error: authError } = await supabase.auth.signUp({\n        email: userData.email,\n        password: userData.password\n    });\n    if (authError) {\n        throw new Error(authError.message);\n    }\n    if (!authData.user) {\n        throw new Error(\"فشل في إنشاء المستخدم\");\n    }\n    // إضافة بيانات المستخدم إلى جدول المستخدمين\n    const { data: userProfile, error: profileError } = await supabase.from(\"users\").insert({\n        id: authData.user.id,\n        email: userData.email,\n        full_name: userData.full_name,\n        phone: userData.phone,\n        role: userData.role,\n        camp_id: userData.camp_id,\n        company_id: userData.company_id\n    }).select().single();\n    if (profileError) {\n        throw new Error(profileError.message);\n    }\n    return {\n        authData,\n        userProfile\n    };\n};\n// دالة للحصول على الكمبات حسب الشركة\nconst getCampsByCompany = async (companyId)=>{\n    const { data, error } = await supabase.from(\"camps\").select(\"*\").eq(\"company_id\", companyId).eq(\"is_active\", true).order(\"name\");\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n// دالة للحصول على جميع الشركات\nconst getCompanies = async ()=>{\n    const { data, error } = await supabase.from(\"companies\").select(\"*\").order(\"name\");\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFxRDtBQUdyRCxNQUFNQyxjQUFjQywwQ0FBb0M7QUFDeEQsTUFBTUcsa0JBQWtCSCxrTkFBeUM7QUFFMUQsTUFBTUssV0FBV1AsbUVBQVlBLENBQVdDLGFBQWFJLGlCQUFpQjtBQUU3RSxrQ0FBa0M7QUFDM0IsTUFBTUcsaUJBQWlCO0lBQzVCLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosU0FBU0ssSUFBSSxDQUFDQyxPQUFPO0lBQzdELElBQUlGLE9BQU87UUFDVEcsUUFBUUgsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBTztJQUNUO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0FBRUYsa0NBQWtDO0FBQzNCLE1BQU1LLHVCQUF1QixPQUFPQztJQUN6QyxNQUFNTixPQUFPLE1BQU1GO0lBQ25CLElBQUksQ0FBQ0UsTUFBTSxPQUFPO0lBRWxCLE1BQU0sRUFBRUQsTUFBTVEsV0FBVyxFQUFFTixLQUFLLEVBQUUsR0FBRyxNQUFNSixTQUN4Q1csSUFBSSxDQUFDLFNBQ0xDLE1BQU0sQ0FBQyw2QkFDUEMsRUFBRSxDQUFDLE1BQU1WLEtBQUtXLEVBQUUsRUFDaEJDLE1BQU07SUFFVCxJQUFJWCxTQUFTLENBQUNNLGFBQWEsT0FBTztJQUVsQywyQkFBMkI7SUFDM0IsT0FBUUQ7UUFDTixLQUFLO1lBQ0gsT0FBT0MsWUFBWU0sSUFBSSxLQUFLO1FBQzlCLEtBQUs7WUFDSCxPQUFPO2dCQUFDO2dCQUFTO2FBQWUsQ0FBQ0MsUUFBUSxDQUFDUCxZQUFZTSxJQUFJO1FBQzVELEtBQUs7WUFDSCxPQUFPO2dCQUFDO2dCQUFTO2dCQUFnQjthQUFhLENBQUNDLFFBQVEsQ0FBQ1AsWUFBWU0sSUFBSTtRQUMxRSxLQUFLO1lBQ0gsT0FBTztnQkFBQztnQkFBUztnQkFBZ0I7Z0JBQWM7YUFBVyxDQUFDQyxRQUFRLENBQUNQLFlBQVlNLElBQUk7UUFDdEY7WUFDRSxPQUFPO0lBQ1g7QUFDRixFQUFFO0FBRUYsMkNBQTJDO0FBQ3BDLE1BQU1FLGlCQUFpQjtJQUM1QixNQUFNZixPQUFPLE1BQU1GO0lBQ25CLElBQUksQ0FBQ0UsTUFBTSxPQUFPO0lBRWxCLE1BQU0sRUFBRUQsTUFBTVEsV0FBVyxFQUFFTixLQUFLLEVBQUUsR0FBRyxNQUFNSixTQUN4Q1csSUFBSSxDQUFDLFNBQ0xDLE1BQU0sQ0FBRSxzRUFLUkMsRUFBRSxDQUFDLE1BQU1WLEtBQUtXLEVBQUUsRUFDaEJDLE1BQU07SUFFVCxJQUFJWCxPQUFPO1FBQ1RHLFFBQVFILEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU87SUFDVDtJQUVBLE9BQU9NO0FBQ1QsRUFBRTtBQUVGLHFCQUFxQjtBQUNkLE1BQU1TLFNBQVMsT0FBT0MsT0FBZUM7SUFDMUMsTUFBTSxFQUFFbkIsSUFBSSxFQUFFRSxLQUFLLEVBQUUsR0FBRyxNQUFNSixTQUFTSyxJQUFJLENBQUNpQixrQkFBa0IsQ0FBQztRQUM3REY7UUFDQUM7SUFDRjtJQUVBLElBQUlqQixPQUFPO1FBQ1QsTUFBTSxJQUFJbUIsTUFBTW5CLE1BQU1vQixPQUFPO0lBQy9CO0lBRUEsT0FBT3RCO0FBQ1QsRUFBRTtBQUVGLHFCQUFxQjtBQUNkLE1BQU11QixVQUFVO0lBQ3JCLE1BQU0sRUFBRXJCLEtBQUssRUFBRSxHQUFHLE1BQU1KLFNBQVNLLElBQUksQ0FBQ29CLE9BQU87SUFDN0MsSUFBSXJCLE9BQU87UUFDVCxNQUFNLElBQUltQixNQUFNbkIsTUFBTW9CLE9BQU87SUFDL0I7QUFDRixFQUFFO0FBRUYsMEJBQTBCO0FBQ25CLE1BQU1FLGFBQWEsT0FBT0M7SUFTL0Isa0NBQWtDO0lBQ2xDLE1BQU0sRUFBRXpCLE1BQU0wQixRQUFRLEVBQUV4QixPQUFPeUIsU0FBUyxFQUFFLEdBQUcsTUFBTTdCLFNBQVNLLElBQUksQ0FBQ3lCLE1BQU0sQ0FBQztRQUN0RVYsT0FBT08sU0FBU1AsS0FBSztRQUNyQkMsVUFBVU0sU0FBU04sUUFBUTtJQUM3QjtJQUVBLElBQUlRLFdBQVc7UUFDYixNQUFNLElBQUlOLE1BQU1NLFVBQVVMLE9BQU87SUFDbkM7SUFFQSxJQUFJLENBQUNJLFNBQVN6QixJQUFJLEVBQUU7UUFDbEIsTUFBTSxJQUFJb0IsTUFBTTtJQUNsQjtJQUVBLDRDQUE0QztJQUM1QyxNQUFNLEVBQUVyQixNQUFNUSxXQUFXLEVBQUVOLE9BQU8yQixZQUFZLEVBQUUsR0FBRyxNQUFNL0IsU0FDdERXLElBQUksQ0FBQyxTQUNMcUIsTUFBTSxDQUFDO1FBQ05sQixJQUFJYyxTQUFTekIsSUFBSSxDQUFDVyxFQUFFO1FBQ3BCTSxPQUFPTyxTQUFTUCxLQUFLO1FBQ3JCYSxXQUFXTixTQUFTTSxTQUFTO1FBQzdCQyxPQUFPUCxTQUFTTyxLQUFLO1FBQ3JCbEIsTUFBTVcsU0FBU1gsSUFBSTtRQUNuQm1CLFNBQVNSLFNBQVNRLE9BQU87UUFDekJDLFlBQVlULFNBQVNTLFVBQVU7SUFDakMsR0FDQ3hCLE1BQU0sR0FDTkcsTUFBTTtJQUVULElBQUlnQixjQUFjO1FBQ2hCLE1BQU0sSUFBSVIsTUFBTVEsYUFBYVAsT0FBTztJQUN0QztJQUVBLE9BQU87UUFBRUk7UUFBVWxCO0lBQVk7QUFDakMsRUFBRTtBQUVGLHFDQUFxQztBQUM5QixNQUFNMkIsb0JBQW9CLE9BQU9DO0lBQ3RDLE1BQU0sRUFBRXBDLElBQUksRUFBRUUsS0FBSyxFQUFFLEdBQUcsTUFBTUosU0FDM0JXLElBQUksQ0FBQyxTQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGNBQWN5QixXQUNqQnpCLEVBQUUsQ0FBQyxhQUFhLE1BQ2hCMEIsS0FBSyxDQUFDO0lBRVQsSUFBSW5DLE9BQU87UUFDVCxNQUFNLElBQUltQixNQUFNbkIsTUFBTW9CLE9BQU87SUFDL0I7SUFFQSxPQUFPdEI7QUFDVCxFQUFFO0FBRUYsK0JBQStCO0FBQ3hCLE1BQU1zQyxlQUFlO0lBQzFCLE1BQU0sRUFBRXRDLElBQUksRUFBRUUsS0FBSyxFQUFFLEdBQUcsTUFBTUosU0FDM0JXLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUMsS0FDUDJCLEtBQUssQ0FBQztJQUVULElBQUluQyxPQUFPO1FBQ1QsTUFBTSxJQUFJbUIsTUFBTW5CLE1BQU1vQixPQUFPO0lBQy9CO0lBRUEsT0FBT3RCO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL3N1cGFiYXNlLnRzPzA2ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcbmltcG9ydCB7IERhdGFiYXNlIH0gZnJvbSAnQC90eXBlcy9zdXBhYmFzZSc7XG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMITtcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZITtcblxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50PERhdGFiYXNlPihzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5KTtcblxuLy8g2K/Yp9mE2Kkg2YTZhNit2LXZiNmEINi52YTZiSDYp9mE2YXYs9iq2K7Yr9mFINin2YTYrdin2YTZilxuZXhwb3J0IGNvbnN0IGdldEN1cnJlbnRVc2VyID0gYXN5bmMgKCkgPT4ge1xuICBjb25zdCB7IGRhdGE6IHsgdXNlciB9LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XG4gIGlmIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgY3VycmVudCB1c2VyOicsIGVycm9yKTtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuICByZXR1cm4gdXNlcjtcbn07XG5cbi8vINiv2KfZhNipINmE2YTYqtit2YLZgiDZhdmGINi12YTYp9it2YrYp9iqINin2YTZhdiz2KrYrtiv2YVcbmV4cG9ydCBjb25zdCBjaGVja1VzZXJQZXJtaXNzaW9ucyA9IGFzeW5jIChyZXF1aXJlZFJvbGU6IHN0cmluZykgPT4ge1xuICBjb25zdCB1c2VyID0gYXdhaXQgZ2V0Q3VycmVudFVzZXIoKTtcbiAgaWYgKCF1c2VyKSByZXR1cm4gZmFsc2U7XG5cbiAgY29uc3QgeyBkYXRhOiB1c2VyUHJvZmlsZSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgLmZyb20oJ3VzZXJzJylcbiAgICAuc2VsZWN0KCdyb2xlLCBjYW1wX2lkLCBjb21wYW55X2lkJylcbiAgICAuZXEoJ2lkJywgdXNlci5pZClcbiAgICAuc2luZ2xlKCk7XG5cbiAgaWYgKGVycm9yIHx8ICF1c2VyUHJvZmlsZSkgcmV0dXJuIGZhbHNlO1xuXG4gIC8vINmF2YbYt9mCINin2YTYqtit2YLZgiDZhdmGINin2YTYtdmE2KfYrdmK2KfYqlxuICBzd2l0Y2ggKHJlcXVpcmVkUm9sZSkge1xuICAgIGNhc2UgJ2FkbWluJzpcbiAgICAgIHJldHVybiB1c2VyUHJvZmlsZS5yb2xlID09PSAnYWRtaW4nO1xuICAgIGNhc2UgJ2NhbXBfbWFuYWdlcic6XG4gICAgICByZXR1cm4gWydhZG1pbicsICdjYW1wX21hbmFnZXInXS5pbmNsdWRlcyh1c2VyUHJvZmlsZS5yb2xlKTtcbiAgICBjYXNlICdhY2NvdW50YW50JzpcbiAgICAgIHJldHVybiBbJ2FkbWluJywgJ2NhbXBfbWFuYWdlcicsICdhY2NvdW50YW50J10uaW5jbHVkZXModXNlclByb2ZpbGUucm9sZSk7XG4gICAgY2FzZSAnb3BlcmF0b3InOlxuICAgICAgcmV0dXJuIFsnYWRtaW4nLCAnY2FtcF9tYW5hZ2VyJywgJ2FjY291bnRhbnQnLCAnb3BlcmF0b3InXS5pbmNsdWRlcyh1c2VyUHJvZmlsZS5yb2xlKTtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIGZhbHNlO1xuICB9XG59O1xuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINmF2LnZhNmI2YXYp9iqINin2YTZhdiz2KrYrtiv2YUg2KfZhNmD2KfZhdmE2KlcbmV4cG9ydCBjb25zdCBnZXRVc2VyUHJvZmlsZSA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgdXNlciA9IGF3YWl0IGdldEN1cnJlbnRVc2VyKCk7XG4gIGlmICghdXNlcikgcmV0dXJuIG51bGw7XG5cbiAgY29uc3QgeyBkYXRhOiB1c2VyUHJvZmlsZSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgLmZyb20oJ3VzZXJzJylcbiAgICAuc2VsZWN0KGBcbiAgICAgICosXG4gICAgICBjYW1wOmNhbXBzKCopLFxuICAgICAgY29tcGFueTpjb21wYW5pZXMoKilcbiAgICBgKVxuICAgIC5lcSgnaWQnLCB1c2VyLmlkKVxuICAgIC5zaW5nbGUoKTtcblxuICBpZiAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXIgcHJvZmlsZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gdXNlclByb2ZpbGU7XG59O1xuXG4vLyDYr9in2YTYqSDZhNiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEXG5leHBvcnQgY29uc3Qgc2lnbkluID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgIGVtYWlsLFxuICAgIHBhc3N3b3JkLFxuICB9KTtcblxuICBpZiAoZXJyb3IpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IubWVzc2FnZSk7XG4gIH1cblxuICByZXR1cm4gZGF0YTtcbn07XG5cbi8vINiv2KfZhNipINmE2KrYs9is2YrZhCDYp9mE2K7YsdmI2KxcbmV4cG9ydCBjb25zdCBzaWduT3V0ID0gYXN5bmMgKCkgPT4ge1xuICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcbiAgaWYgKGVycm9yKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLm1lc3NhZ2UpO1xuICB9XG59O1xuXG4vLyDYr9in2YTYqSDZhNil2YbYtNin2KEg2YXYs9iq2K7Yr9mFINis2K/ZitivXG5leHBvcnQgY29uc3QgY3JlYXRlVXNlciA9IGFzeW5jICh1c2VyRGF0YToge1xuICBlbWFpbDogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBmdWxsX25hbWU6IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIHJvbGU6IHN0cmluZztcbiAgY2FtcF9pZD86IHN0cmluZztcbiAgY29tcGFueV9pZD86IHN0cmluZztcbn0pID0+IHtcbiAgLy8g2KXZhti02KfYoSDYp9mE2YXYs9iq2K7Yr9mFINmB2Yog2YbYuNin2YUg2KfZhNmF2LXYp9iv2YLYqVxuICBjb25zdCB7IGRhdGE6IGF1dGhEYXRhLCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25VcCh7XG4gICAgZW1haWw6IHVzZXJEYXRhLmVtYWlsLFxuICAgIHBhc3N3b3JkOiB1c2VyRGF0YS5wYXNzd29yZCxcbiAgfSk7XG5cbiAgaWYgKGF1dGhFcnJvcikge1xuICAgIHRocm93IG5ldyBFcnJvcihhdXRoRXJyb3IubWVzc2FnZSk7XG4gIH1cblxuICBpZiAoIWF1dGhEYXRhLnVzZXIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ9mB2LTZhCDZgdmKINil2YbYtNin2KEg2KfZhNmF2LPYqtiu2K/ZhScpO1xuICB9XG5cbiAgLy8g2KXYttin2YHYqSDYqNmK2KfZhtin2Kog2KfZhNmF2LPYqtiu2K/ZhSDYpdmE2Ykg2KzYr9mI2YQg2KfZhNmF2LPYqtiu2K/ZhdmK2YZcbiAgY29uc3QgeyBkYXRhOiB1c2VyUHJvZmlsZSwgZXJyb3I6IHByb2ZpbGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAuZnJvbSgndXNlcnMnKVxuICAgIC5pbnNlcnQoe1xuICAgICAgaWQ6IGF1dGhEYXRhLnVzZXIuaWQsXG4gICAgICBlbWFpbDogdXNlckRhdGEuZW1haWwsXG4gICAgICBmdWxsX25hbWU6IHVzZXJEYXRhLmZ1bGxfbmFtZSxcbiAgICAgIHBob25lOiB1c2VyRGF0YS5waG9uZSxcbiAgICAgIHJvbGU6IHVzZXJEYXRhLnJvbGUsXG4gICAgICBjYW1wX2lkOiB1c2VyRGF0YS5jYW1wX2lkLFxuICAgICAgY29tcGFueV9pZDogdXNlckRhdGEuY29tcGFueV9pZCxcbiAgICB9KVxuICAgIC5zZWxlY3QoKVxuICAgIC5zaW5nbGUoKTtcblxuICBpZiAocHJvZmlsZUVycm9yKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKHByb2ZpbGVFcnJvci5tZXNzYWdlKTtcbiAgfVxuXG4gIHJldHVybiB7IGF1dGhEYXRhLCB1c2VyUHJvZmlsZSB9O1xufTtcblxuLy8g2K/Yp9mE2Kkg2YTZhNit2LXZiNmEINi52YTZiSDYp9mE2YPZhdio2KfYqiDYrdiz2Kgg2KfZhNi02LHZg9ipXG5leHBvcnQgY29uc3QgZ2V0Q2FtcHNCeUNvbXBhbnkgPSBhc3luYyAoY29tcGFueUlkOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAuZnJvbSgnY2FtcHMnKVxuICAgIC5zZWxlY3QoJyonKVxuICAgIC5lcSgnY29tcGFueV9pZCcsIGNvbXBhbnlJZClcbiAgICAuZXEoJ2lzX2FjdGl2ZScsIHRydWUpXG4gICAgLm9yZGVyKCduYW1lJyk7XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLm1lc3NhZ2UpO1xuICB9XG5cbiAgcmV0dXJuIGRhdGE7XG59O1xuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINis2YXZiti5INin2YTYtNix2YPYp9iqXG5leHBvcnQgY29uc3QgZ2V0Q29tcGFuaWVzID0gYXN5bmMgKCkgPT4ge1xuICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdjb21wYW5pZXMnKVxuICAgIC5zZWxlY3QoJyonKVxuICAgIC5vcmRlcignbmFtZScpO1xuXG4gIGlmIChlcnJvcikge1xuICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5tZXNzYWdlKTtcbiAgfVxuXG4gIHJldHVybiBkYXRhO1xufTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUFub25LZXkiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsInN1cGFiYXNlIiwiZ2V0Q3VycmVudFVzZXIiLCJkYXRhIiwidXNlciIsImVycm9yIiwiYXV0aCIsImdldFVzZXIiLCJjb25zb2xlIiwiY2hlY2tVc2VyUGVybWlzc2lvbnMiLCJyZXF1aXJlZFJvbGUiLCJ1c2VyUHJvZmlsZSIsImZyb20iLCJzZWxlY3QiLCJlcSIsImlkIiwic2luZ2xlIiwicm9sZSIsImluY2x1ZGVzIiwiZ2V0VXNlclByb2ZpbGUiLCJzaWduSW4iLCJlbWFpbCIsInBhc3N3b3JkIiwic2lnbkluV2l0aFBhc3N3b3JkIiwiRXJyb3IiLCJtZXNzYWdlIiwic2lnbk91dCIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsImF1dGhEYXRhIiwiYXV0aEVycm9yIiwic2lnblVwIiwicHJvZmlsZUVycm9yIiwiaW5zZXJ0IiwiZnVsbF9uYW1lIiwicGhvbmUiLCJjYW1wX2lkIiwiY29tcGFueV9pZCIsImdldENhbXBzQnlDb21wYW55IiwiY29tcGFueUlkIiwib3JkZXIiLCJnZXRDb21wYW5pZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});