import { supabase } from './supabase';
import { Operation, CreateOperationData, ReportFilter, PaginatedResponse, DashboardStats } from '@/types/database';

// الحصول على جميع العمليات مع التصفية والترقيم
export const getOperations = async (
  filter: ReportFilter = {}
): Promise<PaginatedResponse<Operation>> => {
  try {
    const {
      camp_id,
      driver_id,
      material_id,
      operation_type,
      start_date,
      end_date,
      page = 1,
      limit = 10
    } = filter;

    let query = supabase
      .from('operations')
      .select(`
        *,
        camp:camps(id, name, location),
        driver:drivers(id, name, phone, vehicle_number),
        material:materials(id, name, unit),
        operator:users(id, full_name)
      `, { count: 'exact' });

    // تطبيق الفلاتر
    if (camp_id) {
      query = query.eq('camp_id', camp_id);
    }

    if (driver_id) {
      query = query.eq('driver_id', driver_id);
    }

    if (material_id) {
      query = query.eq('material_id', material_id);
    }

    if (operation_type) {
      query = query.eq('operation_type', operation_type);
    }

    if (start_date) {
      query = query.gte('operation_date', start_date);
    }

    if (end_date) {
      query = query.lte('operation_date', end_date);
    }

    // تطبيق الترقيم
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await query
      .range(from, to)
      .order('operation_date', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      total_pages: totalPages
    };
  } catch (error) {
    console.error('Error fetching operations:', error);
    throw error;
  }
};

// الحصول على عملية واحدة
export const getOperation = async (id: string): Promise<Operation | null> => {
  try {
    const { data, error } = await supabase
      .from('operations')
      .select(`
        *,
        camp:camps(id, name, location),
        driver:drivers(id, name, phone, vehicle_number),
        material:materials(id, name, unit),
        operator:users(id, full_name)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error fetching operation:', error);
    throw error;
  }
};

// إنشاء عملية جديدة
export const createOperation = async (
  operationData: CreateOperationData,
  operatorId: string
): Promise<Operation> => {
  try {
    // الحصول على سعر المادة
    const { data: material, error: materialError } = await supabase
      .from('materials')
      .select('unit_price')
      .eq('id', operationData.material_id)
      .single();

    if (materialError) {
      throw new Error('فشل في الحصول على بيانات المادة');
    }

    const { data, error } = await supabase
      .from('operations')
      .insert({
        ...operationData,
        unit_price: material.unit_price,
        operator_id: operatorId,
        operation_date: new Date().toISOString()
      })
      .select(`
        *,
        camp:camps(id, name, location),
        driver:drivers(id, name, phone, vehicle_number),
        material:materials(id, name, unit),
        operator:users(id, full_name)
      `)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error creating operation:', error);
    throw error;
  }
};

// تحديث عملية
export const updateOperation = async (
  id: string,
  updateData: Partial<CreateOperationData>
): Promise<Operation> => {
  try {
    const { data, error } = await supabase
      .from('operations')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        camp:camps(id, name, location),
        driver:drivers(id, name, phone, vehicle_number),
        material:materials(id, name, unit),
        operator:users(id, full_name)
      `)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error updating operation:', error);
    throw error;
  }
};

// حذف عملية
export const deleteOperation = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('operations')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }
  } catch (error) {
    console.error('Error deleting operation:', error);
    throw error;
  }
};

// الحصول على إحصائيات لوحة التحكم
export const getDashboardStats = async (campId?: string): Promise<DashboardStats> => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    let operationsQuery = supabase.from('operations').select('*');
    let driversQuery = supabase.from('drivers').select('id').eq('is_active', true);

    if (campId) {
      operationsQuery = operationsQuery.eq('camp_id', campId);
      driversQuery = driversQuery.eq('camp_id', campId);
    }

    // الحصول على العمليات
    const { data: operations, error: operationsError } = await operationsQuery;
    if (operationsError) throw new Error(operationsError.message);

    // الحصول على السائقين النشطين
    const { data: drivers, error: driversError } = await driversQuery;
    if (driversError) throw new Error(driversError.message);

    // حساب الإحصائيات
    const todayOperations = operations?.filter(op => 
      new Date(op.operation_date) >= startOfDay
    ) || [];

    const monthOperations = operations?.filter(op => 
      new Date(op.operation_date) >= startOfMonth
    ) || [];

    // إحصائيات حسب المادة
    const materialStats = new Map();
    operations?.forEach(op => {
      const key = op.material_id;
      if (!materialStats.has(key)) {
        materialStats.set(key, { count: 0, total_amount: 0, material_name: '' });
      }
      const stats = materialStats.get(key);
      stats.count++;
      stats.total_amount += op.total_amount;
    });

    // الحصول على أسماء المواد
    const materialIds = Array.from(materialStats.keys());
    if (materialIds.length > 0) {
      const { data: materials } = await supabase
        .from('materials')
        .select('id, name')
        .in('id', materialIds);

      materials?.forEach(material => {
        if (materialStats.has(material.id)) {
          materialStats.get(material.id).material_name = material.name;
        }
      });
    }

    // إحصائيات حسب نوع العملية
    const operationTypeStats = [
      {
        type: 'entry' as const,
        count: operations?.filter(op => op.operation_type === 'entry').length || 0,
        total_amount: operations?.filter(op => op.operation_type === 'entry')
          .reduce((sum, op) => sum + op.total_amount, 0) || 0
      },
      {
        type: 'exit' as const,
        count: operations?.filter(op => op.operation_type === 'exit').length || 0,
        total_amount: operations?.filter(op => op.operation_type === 'exit')
          .reduce((sum, op) => sum + op.total_amount, 0) || 0
      }
    ];

    return {
      total_operations_today: todayOperations.length,
      total_amount_today: todayOperations.reduce((sum, op) => sum + op.total_amount, 0),
      active_drivers: drivers?.length || 0,
      total_operations_month: monthOperations.length,
      total_amount_month: monthOperations.reduce((sum, op) => sum + op.total_amount, 0),
      operations_by_material: Array.from(materialStats.values()),
      operations_by_type: operationTypeStats
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

// الحصول على العمليات الأخيرة
export const getRecentOperations = async (
  campId?: string,
  limit: number = 10
): Promise<Operation[]> => {
  try {
    let query = supabase
      .from('operations')
      .select(`
        *,
        camp:camps(id, name),
        driver:drivers(id, name, vehicle_number),
        material:materials(id, name),
        operator:users(id, full_name)
      `);

    if (campId) {
      query = query.eq('camp_id', campId);
    }

    const { data, error } = await query
      .order('operation_date', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching recent operations:', error);
    throw error;
  }
};

// الحصول على إحصائيات يومية
export const getDailyStats = async (
  campId?: string,
  days: number = 7
): Promise<any[]> => {
  try {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    let query = supabase
      .from('operations')
      .select('operation_date, operation_type, total_amount')
      .gte('operation_date', startDate.toISOString())
      .lte('operation_date', endDate.toISOString());

    if (campId) {
      query = query.eq('camp_id', campId);
    }

    const { data, error } = await query.order('operation_date');

    if (error) {
      throw new Error(error.message);
    }

    // تجميع البيانات حسب اليوم
    const dailyStats = new Map();
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(endDate.getDate() - i);
      const dateKey = date.toISOString().split('T')[0];
      dailyStats.set(dateKey, {
        date: dateKey,
        entry_count: 0,
        exit_count: 0,
        total_amount: 0
      });
    }

    data?.forEach(op => {
      const dateKey = op.operation_date.split('T')[0];
      if (dailyStats.has(dateKey)) {
        const stats = dailyStats.get(dateKey);
        if (op.operation_type === 'entry') {
          stats.entry_count++;
        } else {
          stats.exit_count++;
        }
        stats.total_amount += op.total_amount;
      }
    });

    return Array.from(dailyStats.values()).reverse();
  } catch (error) {
    console.error('Error fetching daily stats:', error);
    throw error;
  }
};
