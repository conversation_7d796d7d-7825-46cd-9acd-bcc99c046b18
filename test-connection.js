// اختبار اتصال قاعدة البيانات
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 اختبار اتصال قاعدة البيانات...\n');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ متغيرات البيئة غير موجودة!');
  console.log('تأكد من وجود ملف .env.local مع:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL');
  console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

console.log('✅ متغيرات البيئة موجودة');
console.log(`📡 Supabase URL: ${supabaseUrl}`);
console.log(`🔑 API Key: ${supabaseKey.substring(0, 20)}...`);

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('\n🔄 اختبار الاتصال الأساسي...');
    
    // اختبار 1: الاتصال الأساسي
    const { data, error } = await supabase
      .from('companies')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ خطأ في الاتصال:', error.message);
      return false;
    }
    
    console.log('✅ الاتصال الأساسي يعمل');
    
    // اختبار 2: فحص الجداول
    console.log('\n🔄 فحص الجداول...');
    
    const tables = ['companies', 'camps', 'users', 'drivers', 'materials', 'operations'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count')
          .limit(1);
        
        if (error) {
          console.log(`❌ جدول ${table}: ${error.message}`);
        } else {
          console.log(`✅ جدول ${table}: موجود`);
        }
      } catch (err) {
        console.log(`❌ جدول ${table}: خطأ في الوصول`);
      }
    }
    
    // اختبار 3: فحص البيانات التجريبية
    console.log('\n🔄 فحص البيانات التجريبية...');
    
    const { data: companies } = await supabase
      .from('companies')
      .select('*');
    
    console.log(`📊 عدد الشركات: ${companies?.length || 0}`);
    
    const { data: camps } = await supabase
      .from('camps')
      .select('*');
    
    console.log(`🏕️ عدد الكمبات: ${camps?.length || 0}`);
    
    const { data: materials } = await supabase
      .from('materials')
      .select('*');
    
    console.log(`📦 عدد المواد: ${materials?.length || 0}`);
    
    // اختبار 4: فحص المصادقة
    console.log('\n🔄 فحص نظام المصادقة...');
    
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.log('⚠️ لا توجد جلسة نشطة (هذا طبيعي)');
    } else {
      console.log('✅ نظام المصادقة يعمل');
    }
    
    // اختبار 5: فحص المستخدمين
    console.log('\n🔄 فحص المستخدمين...');
    
    const { data: users } = await supabase
      .from('users')
      .select('email, role');
    
    console.log(`👥 عدد المستخدمين: ${users?.length || 0}`);
    
    if (users && users.length > 0) {
      console.log('📋 المستخدمين الموجودين:');
      users.forEach(user => {
        console.log(`   - ${user.email} (${user.role})`);
      });
    }
    
    console.log('\n🎉 اختبار الاتصال مكتمل!');
    
    return true;
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
    return false;
  }
}

// تشغيل الاختبار
testConnection().then(success => {
  if (success) {
    console.log('\n✅ قاعدة البيانات متصلة بنجاح!');
    console.log('\n📋 الخطوات التالية:');
    console.log('1. إذا لم تكن هناك بيانات تجريبية، شغل ملف database/sample_data.sql');
    console.log('2. أنشئ مستخدمين في Supabase Auth');
    console.log('3. أضف بيانات المستخدمين في جدول users');
    console.log('4. جرب تسجيل الدخول في النظام');
  } else {
    console.log('\n❌ هناك مشاكل في الاتصال!');
    console.log('\n🔧 حلول مقترحة:');
    console.log('1. تأكد من تشغيل ملف database/schema.sql في Supabase');
    console.log('2. تحقق من صحة متغيرات البيئة');
    console.log('3. تأكد من أن مشروع Supabase نشط');
  }
  
  process.exit(success ? 0 : 1);
});
