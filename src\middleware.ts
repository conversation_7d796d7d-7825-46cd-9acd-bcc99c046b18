import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();

  // تجاهل الملفات الثابتة والـ API
  if (
    req.nextUrl.pathname.startsWith("/_next") ||
    req.nextUrl.pathname.startsWith("/api") ||
    req.nextUrl.pathname.includes(".") ||
    req.nextUrl.pathname === "/favicon.ico"
  ) {
    return res;
  }

  const supabase = createMiddlewareClient({ req, res });

  try {
    // تحديث الجلسة
    const {
      data: { session },
    } = await supabase.auth.getSession();

    // المسارات المحمية
    const protectedPaths = ["/dashboard"];
    const isProtectedPath = protectedPaths.some((path) =>
      req.nextUrl.pathname.startsWith(path)
    );

    // إذا كان المسار محمي ولا توجد جلسة، إعادة توجيه لصفحة تسجيل الدخول
    if (isProtectedPath && !session) {
      const redirectUrl = new URL("/login", req.url);
      redirectUrl.searchParams.set("redirectTo", req.nextUrl.pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // إذا كان المستخدم مسجل دخول ويحاول الوصول لصفحة تسجيل الدخول، إعادة توجيه للوحة التحكم
    if (session && req.nextUrl.pathname === "/login") {
      return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    return res;
  } catch (error) {
    console.error("Middleware error:", error);
    return res;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes
     */
    "/((?!_next/static|_next/image|favicon.ico|public/|api/).*)",
  ],
};
