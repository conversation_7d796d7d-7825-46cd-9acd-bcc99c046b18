# 🚀 دليل البدء السريع - نظام إدارة سيارات الحمل

## ⚡ البدء في 5 دقائق

### 1. إعداد Supabase (دقيقتان)

#### أ. إنشاء مشروع

1. اذهب إلى [supabase.com](https://supabase.com)
2. انقر "New Project"
3. اسم المشروع: `car-management`
4. كلمة مرور قوية لقاعدة البيانات

#### ب. إعداد قاعدة البيانات

1. اذهب إلى SQL Editor
2. انسخ والصق محتوى `database/schema.sql`
3. انقر "Run"
4. انسخ والصق محتوى `database/sample_data.sql`
5. انقر "Run"

### 2. إعداد المشروع (دقيقة واحدة)

```bash
# نسخ ملف البيئة
cp .env.local.example .env.local

# تحديث المتغيرات (احصل عليها من Supabase > Settings > API)
# NEXT_PUBLIC_SUPABASE_URL=your_url_here
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key_here
```

### 3. تشغيل النظام (دقيقتان)

```bash
# تثبيت التبعيات
npm install

# تشغيل الخادم
npm run dev
```

🎉 **النظام جاهز على**: http://localhost:3000

---

## 🔑 حسابات تجريبية

بعد إنشاء المستخدمين في Supabase Auth:

| الدور       | البريد الإلكتروني      | كلمة المرور |
| ----------- | ---------------------- | ----------- |
| مدير النظام | <EMAIL>      | password123 |
| مدير كمب    | <EMAIL>    | password123 |
| محاسب       | <EMAIL> | password123 |
| عامل        | <EMAIL>   | password123 |

---

## 📱 اختبار سريع

### 1. تسجيل الدخول

- اذهب إلى `/login`
- استخدم `<EMAIL>` / `password123`

### 2. إضافة سائق

- اذهب إلى "السائقين"
- انقر "إضافة سائق جديد"
- املأ البيانات واحفظ

### 3. مسح QR Code

- اذهب إلى "مسح QR"
- انقر "بدء المسح"
- امسح QR Code للسائق

### 4. عرض التقارير

- اذهب إلى "التقارير"
- اختر فترة زمنية
- شاهد الإحصائيات

---

## 🛠️ حل المشاكل السريع

### مشكلة: لا يعمل npm

```bash
# استخدم npx بدلاً من npm
npx next dev
```

### مشكلة: خطأ في قاعدة البيانات

1. تأكد من تشغيل SQL scripts
2. تحقق من متغيرات البيئة
3. تأكد من صحة URL و Keys

### مشكلة: لا يعمل QR Scanner

1. تأكد من السماح بالوصول للكاميرا
2. استخدم HTTPS في الإنتاج
3. جرب متصفح مختلف

### مشكلة: خطأ في المصادقة

1. تحقق من Site URL في Supabase
2. تأكد من إنشاء المستخدمين
3. تحقق من جدول users

---

## 📋 قائمة مراجعة سريعة

- [ ] إنشاء مشروع Supabase
- [ ] تشغيل SQL scripts
- [ ] تحديث .env.local
- [ ] تثبيت التبعيات
- [ ] تشغيل المشروع
- [ ] إنشاء مستخدم تجريبي
- [ ] اختبار تسجيل الدخول
- [ ] إضافة سائق تجريبي
- [ ] اختبار QR Code

---

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

1. **إضافة بيانات حقيقية**:

   - إنشاء شركتك
   - إضافة الكمبات
   - إنشاء المستخدمين

2. **تخصيص النظام**:

   - تحديث الألوان والشعار
   - إضافة مواد جديدة
   - تخصيص التقارير

3. **النشر**:

   - استخدام Vercel للنشر السريع
   - إعداد النطاق المخصص
   - تفعيل HTTPS

4. **التدريب**:
   - تدريب المستخدمين
   - إعداد دليل الاستخدام
   - تحديد المسؤوليات

---

## 📞 الحصول على المساعدة

### مصادر مفيدة:

- [وثائق Supabase](https://supabase.com/docs)
- [وثائق Next.js](https://nextjs.org/docs)
- [وثائق Tailwind CSS](https://tailwindcss.com/docs)

### في حالة المشاكل:

1. راجع ملف SETUP.md للتفاصيل
2. تحقق من سجلات المتصفح (F12)
3. راجع سجلات Supabase
4. اتصل بالدعم التقني

---

**نصيحة**: احتفظ بنسخة احتياطية من قاعدة البيانات قبل إضافة البيانات الحقيقية!

---

## ✅ مشاكل التوجيه المحلولة

### المشكلة: إعادة توجيه تلقائية للصفحة الرئيسية

**الحل**: تم إصلاح middleware ليسمح بعرض الصفحة الرئيسية

### المشكلة: عدم التوجيه بعد تسجيل الدخول

**الحل**: تم تحسين عملية التوجيه باستخدام `window.location.href`

### كيفية الاستخدام الآن:

1. **افتح**: `http://localhost:3000` - ستظهر الصفحة الرئيسية
2. **انقر**: "تسجيل الدخول"
3. **أدخل**: `<EMAIL>` / `password123`
4. **سيتم توجيهك**: تلقائياً إلى `/dashboard`

### الصلاحيات حسب الدور:

- **مدير النظام**: جميع الصفحات
- **مدير كمب**: كل شيء عدا إدارة الكمبات
- **محاسب**: التقارير والعرض فقط
- **عامل**: مسح QR والعمليات الأساسية
