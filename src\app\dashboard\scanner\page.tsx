'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import QRScanner from '@/components/QRScanner';
import { getQRCodeInfo } from '@/lib/qr-generator';
import { getDriverByQRCode } from '@/lib/drivers';
import { createOperation } from '@/lib/operations';
import { supabase } from '@/lib/supabase';
import { Driver, Material, CreateOperationData } from '@/types/database';

export default function ScannerPage() {
  const [isScanning, setIsScanning] = useState(false);
  const [scannedDriver, setScannedDriver] = useState<Driver | null>(null);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [selectedMaterial, setSelectedMaterial] = useState<string>('');
  const [operationType, setOperationType] = useState<'entry' | 'exit'>('entry');
  const [quantity, setQuantity] = useState<number>(1);
  const [notes, setNotes] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    loadMaterials();
    loadCurrentUser();
  }, []);

  const loadMaterials = async () => {
    try {
      const { data, error } = await supabase
        .from('materials')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setMaterials(data || []);
      if (data && data.length > 0) {
        setSelectedMaterial(data[0].id);
      }
    } catch (error) {
      console.error('Error loading materials:', error);
      setMessage({ type: 'error', text: 'فشل في تحميل المواد' });
    }
  };

  const loadCurrentUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: userProfile } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();
        setCurrentUser(userProfile);
      }
    } catch (error) {
      console.error('Error loading current user:', error);
    }
  };

  const handleQRScan = async (qrData: string) => {
    setIsProcessing(true);
    setMessage(null);

    try {
      // فك تشفير بيانات QR Code
      const qrInfo = getQRCodeInfo(qrData);
      
      if (!qrInfo.isValid || qrInfo.type !== 'driver') {
        setMessage({ type: 'error', text: 'رمز QR غير صحيح أو غير مدعوم' });
        setIsProcessing(false);
        return;
      }

      // البحث عن السائق في قاعدة البيانات
      const driver = await getDriverByQRCode(qrInfo.driverCode);
      
      if (!driver) {
        setMessage({ type: 'error', text: 'السائق غير موجود أو غير نشط' });
        setIsProcessing(false);
        return;
      }

      // التحقق من صلاحية المستخدم للوصول لهذا الكمب
      if (currentUser && currentUser.role !== 'admin' && currentUser.camp_id !== driver.camp_id) {
        setMessage({ type: 'error', text: 'ليس لديك صلاحية للوصول لهذا السائق' });
        setIsProcessing(false);
        return;
      }

      setScannedDriver(driver);
      setMessage({ type: 'success', text: `تم مسح بيانات السائق: ${driver.name}` });
      setIsScanning(false);
    } catch (error) {
      console.error('Error processing QR scan:', error);
      setMessage({ type: 'error', text: 'حدث خطأ في معالجة رمز QR' });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSubmitOperation = async () => {
    if (!scannedDriver || !selectedMaterial || !currentUser) {
      setMessage({ type: 'error', text: 'يرجى التأكد من جميع البيانات المطلوبة' });
      return;
    }

    setIsProcessing(true);
    setMessage(null);

    try {
      const operationData: CreateOperationData = {
        driver_id: scannedDriver.id,
        material_id: selectedMaterial,
        operation_type: operationType,
        quantity: quantity,
        notes: notes.trim() || undefined,
        camp_id: scannedDriver.camp_id
      };

      await createOperation(operationData, currentUser.id);

      setMessage({ 
        type: 'success', 
        text: `تم تسجيل عملية ${operationType === 'entry' ? 'الدخول' : 'الخروج'} بنجاح` 
      });

      // إعادة تعيين النموذج
      setScannedDriver(null);
      setQuantity(1);
      setNotes('');
      setOperationType('entry');
    } catch (error) {
      console.error('Error creating operation:', error);
      setMessage({ type: 'error', text: 'فشل في تسجيل العملية' });
    } finally {
      setIsProcessing(false);
    }
  };

  const resetScanner = () => {
    setScannedDriver(null);
    setMessage(null);
    setQuantity(1);
    setNotes('');
    setOperationType('entry');
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">مسح QR Code</h1>
          <p className="mt-1 text-sm text-gray-600">
            امسح رمز QR الخاص بالسائق لتسجيل عملية جديدة
          </p>
        </div>

        {/* Message */}
        {message && (
          <div className={`p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-success-50 text-success-700 border border-success-200' 
              : 'bg-danger-50 text-danger-700 border border-danger-200'
          }`}>
            <div className="flex items-center">
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {message.type === 'success' ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                )}
              </svg>
              {message.text}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* QR Scanner */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">مسح رمز QR</h3>
            
            {!scannedDriver ? (
              <div>
                <div className="mb-4">
                  <button
                    onClick={() => setIsScanning(!isScanning)}
                    className={`btn-primary w-full ${isScanning ? 'bg-danger-600 hover:bg-danger-700' : ''}`}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري المعالجة...
                      </div>
                    ) : isScanning ? (
                      'إيقاف المسح'
                    ) : (
                      'بدء المسح'
                    )}
                  </button>
                </div>

                {isScanning && (
                  <QRScanner
                    onScan={handleQRScan}
                    onError={(error) => setMessage({ type: 'error', text: error })}
                    isActive={isScanning}
                  />
                )}
              </div>
            ) : (
              <div className="text-center">
                <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">تم مسح البيانات بنجاح</h4>
                <p className="text-gray-600 mb-4">يمكنك الآن تسجيل العملية</p>
                <button
                  onClick={resetScanner}
                  className="btn-secondary"
                >
                  مسح سائق آخر
                </button>
              </div>
            )}
          </div>

          {/* Operation Form */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">تسجيل العملية</h3>
            
            {scannedDriver ? (
              <div className="space-y-4">
                {/* Driver Info */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">بيانات السائق</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">الاسم:</span>
                      <span className="font-medium mr-2">{scannedDriver.name}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">رقم السيارة:</span>
                      <span className="font-medium mr-2">{scannedDriver.vehicle_number}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">نوع السيارة:</span>
                      <span className="font-medium mr-2">{scannedDriver.vehicle_type}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">الهاتف:</span>
                      <span className="font-medium mr-2">{scannedDriver.phone}</span>
                    </div>
                  </div>
                </div>

                {/* Operation Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع العملية
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => setOperationType('entry')}
                      className={`p-3 rounded-lg border-2 transition-colors ${
                        operationType === 'entry'
                          ? 'border-success-500 bg-success-50 text-success-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      دخول
                    </button>
                    <button
                      onClick={() => setOperationType('exit')}
                      className={`p-3 rounded-lg border-2 transition-colors ${
                        operationType === 'exit'
                          ? 'border-danger-500 bg-danger-50 text-danger-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      خروج
                    </button>
                  </div>
                </div>

                {/* Material */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المادة
                  </label>
                  <select
                    value={selectedMaterial}
                    onChange={(e) => setSelectedMaterial(e.target.value)}
                    className="input-field"
                  >
                    {materials.map((material) => (
                      <option key={material.id} value={material.id}>
                        {material.name} - {material.unit_price.toLocaleString()} د.ع/{material.unit}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Quantity */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الكمية
                  </label>
                  <input
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={quantity}
                    onChange={(e) => setQuantity(parseFloat(e.target.value) || 1)}
                    className="input-field"
                  />
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ملاحظات (اختياري)
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={3}
                    className="input-field"
                    placeholder="أدخل أي ملاحظات إضافية..."
                  />
                </div>

                {/* Submit Button */}
                <button
                  onClick={handleSubmitOperation}
                  disabled={isProcessing || !selectedMaterial}
                  className="btn-primary w-full"
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      جاري التسجيل...
                    </div>
                  ) : (
                    `تسجيل عملية ${operationType === 'entry' ? 'الدخول' : 'الخروج'}`
                  )}
                </button>
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                </svg>
                <p className="text-gray-500">امسح رمز QR أولاً لتسجيل العملية</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
